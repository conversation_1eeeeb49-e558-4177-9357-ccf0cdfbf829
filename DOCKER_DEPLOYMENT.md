# Docker Deployment Guide

This guide provides detailed instructions for deploying the Service Management Portal using Docker.

## Prerequisites

- Docker Engine 20.10 or higher
- Docker Compose 2.0 or higher
- At least 2GB RAM available
- 10GB disk space

## Quick Deployment

### 1. Clone Repository
```bash
git clone <repository-url>
cd service_management_portal
```

### 2. Run Deployment Script
```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

### 3. Access Services
- **Portal**: http://localhost:8501
- **Demo Nginx**: http://localhost:8080
- **Demo Apache**: http://localhost:8081
- **Portainer**: http://localhost:9000

## Manual Deployment

### 1. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

### 2. SSH Keys Setup (Optional)
```bash
# Create SSH keys directory
mkdir -p ssh_keys
chmod 700 ssh_keys

# Copy your SSH private key
cp ~/.ssh/id_rsa ssh_keys/
chmod 600 ssh_keys/id_rsa
```

### 3. Deploy Services
```bash
# Start all services
docker-compose up -d

# Wait for database initialization
sleep 15

# Initialize database
docker-compose exec portal python database/init_db.py
```

## Service Architecture

### Core Services

1. **Portal Application** (`portal`)
   - Main Streamlit application
   - Manages both Docker containers and remote services
   - Connects to PostgreSQL database

2. **PostgreSQL Database** (`db`)
   - Primary data storage
   - User accounts, roles, permissions
   - Service configurations and audit logs

### Demo Services

3. **Demo Nginx** (`nginx`)
   - Sample web server for testing
   - Accessible at port 8080
   - Managed through portal

4. **Demo Apache** (`apache`)
   - Sample web server for testing
   - Accessible at port 8081
   - Managed through portal

5. **Demo MySQL** (`mysql`)
   - Sample database server
   - Accessible at port 3306
   - Managed through portal

6. **Demo Redis** (`redis`)
   - Sample cache server
   - Accessible at port 6379
   - Managed through portal

7. **Portainer** (`portainer`)
   - Docker management interface
   - Accessible at port 9000
   - Optional monitoring tool

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `DATABASE_URL` | `**************************************/service_portal` | Database connection string |
| `SECRET_KEY` | `your-secret-key-change-in-production` | Application secret key |
| `SESSION_TIMEOUT` | `3600` | Session timeout in seconds |
| `SSH_TIMEOUT` | `30` | SSH connection timeout |
| `DEBUG` | `false` | Enable debug mode |
| `LOG_LEVEL` | `INFO` | Logging level |
| `MAX_LOG_LINES` | `1000` | Maximum log lines to display |

### Volume Mounts

| Host Path | Container Path | Purpose |
|-----------|----------------|---------|
| `./ssh_keys` | `/home/<USER>/.ssh` | SSH keys for remote servers |
| `./logs` | `/app/logs` | Application logs |
| `/var/run/docker.sock` | `/var/run/docker.sock` | Docker socket for container management |

## Management Commands

### Service Control
```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# Restart specific service
docker-compose restart portal

# View service status
docker-compose ps

# Scale services (if needed)
docker-compose up -d --scale portal=2
```

### Logs and Monitoring
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f portal
docker-compose logs -f db

# View last 100 lines
docker-compose logs --tail=100 portal
```

### Database Management
```bash
# Access database
docker-compose exec db psql -U postgres service_portal

# Backup database
docker-compose exec -T db pg_dump -U postgres service_portal > backup.sql

# Restore database
docker-compose exec -T db psql -U postgres service_portal < backup.sql
```

## Backup and Restore

### Automated Backup
```bash
# Run backup script
./scripts/backup.sh

# Backups are stored in backups/ directory
ls -la backups/
```

### Manual Backup
```bash
# Create backup directory
mkdir -p backups

# Backup database
docker-compose exec -T db pg_dump -U postgres service_portal > backups/db_backup.sql

# Backup configuration
tar -czf backups/config_backup.tar.gz .env ssh_keys/ logs/
```

### Restore from Backup
```bash
# Use restore script
./scripts/restore.sh backups/service_portal_backup_YYYYMMDD_HHMMSS.tar.gz

# Or manual restore
docker-compose down
# Restore files and database
docker-compose up -d
```

## Security Considerations

### Network Security
- All services run in isolated Docker network
- Only necessary ports are exposed
- Database is not directly accessible from outside

### Authentication
- Change default admin credentials immediately
- Use strong passwords and secret keys
- SSH key-based authentication for remote servers

### File Permissions
```bash
# Secure SSH keys
chmod 700 ssh_keys/
chmod 600 ssh_keys/*

# Secure environment file
chmod 600 .env
```

## Monitoring and Maintenance

### Health Checks
```bash
# Check service health
docker-compose ps

# Test portal health
curl -f http://localhost:8501/_stcore/health

# Test database health
docker-compose exec db pg_isready -U postgres
```

### Updates
```bash
# Pull latest images
docker-compose pull

# Restart with new images
docker-compose up -d

# Clean up old images
docker image prune -f
```

### Log Rotation
```bash
# Configure Docker log rotation in daemon.json
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check port usage
   netstat -tulpn | grep :8501
   
   # Change ports in docker-compose.yml if needed
   ```

2. **Database Connection Issues**
   ```bash
   # Check database logs
   docker-compose logs db
   
   # Restart database
   docker-compose restart db
   ```

3. **Permission Denied for SSH**
   ```bash
   # Fix SSH key permissions
   chmod 700 ssh_keys/
   chmod 600 ssh_keys/*
   ```

4. **Container Won't Start**
   ```bash
   # Check logs
   docker-compose logs portal
   
   # Rebuild container
   docker-compose build --no-cache portal
   docker-compose up -d portal
   ```

### Performance Tuning

1. **Database Performance**
   - Increase shared_buffers in PostgreSQL
   - Add connection pooling if needed
   - Monitor query performance

2. **Application Performance**
   - Increase container memory limits
   - Use multiple portal instances if needed
   - Monitor resource usage

3. **Network Performance**
   - Use host networking for better performance
   - Optimize Docker network configuration

## Production Deployment

### Recommendations

1. **Use external database** for production
2. **Set up SSL/TLS** with reverse proxy
3. **Configure backup automation**
4. **Set up monitoring and alerting**
5. **Use secrets management** for sensitive data
6. **Regular security updates**

### Example Production docker-compose.yml
```yaml
version: '3.8'
services:
  portal:
    build: .
    environment:
      - DATABASE_URL=***************************************/service_portal
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - /secure/ssh_keys:/home/<USER>/.ssh:ro
      - /var/log/service_portal:/app/logs
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
```

This completes the Docker deployment guide for the Service Management Portal.
