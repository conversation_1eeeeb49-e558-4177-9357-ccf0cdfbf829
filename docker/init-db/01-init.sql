-- Initialize the service_portal database
-- This script runs when the PostgreSQL container starts for the first time

-- Create database if it doesn't exist (handled by POSTGRES_DB environment variable)
-- The database 'service_portal' is automatically created

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE service_portal TO postgres;

-- Create extensions if needed
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
