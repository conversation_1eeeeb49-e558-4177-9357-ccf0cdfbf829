import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    # Database
    DATABASE_URL = os.getenv("DATABASE_URL", "**************************************/service_portal")
    SQLITE_URL = os.getenv("SQLITE_URL", "sqlite:///database/service_portal.db")
    
    # Security
    SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    SESSION_TIMEOUT = int(os.getenv("SESSION_TIMEOUT", "3600"))  # 1 hour
    
    # Application
    APP_NAME = "Linux System Services & Cron Jobs Management Portal"
    APP_VERSION = "1.0.0"
    DEBUG = os.getenv("DEBUG", "False").lower() == "true"
    
    # SSH Settings
    SSH_TIMEOUT = int(os.getenv("SSH_TIMEOUT", "30"))
    SSH_KEY_PATH = os.getenv("SSH_KEY_PATH", "~/.ssh/id_rsa")
    
    # Logging
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    MAX_LOG_LINES = int(os.getenv("MAX_LOG_LINES", "1000"))
    
    # UI Settings
    PAGE_TITLE = "Linux System Services & Cron Jobs Portal"
    PAGE_ICON = "🐧"
    LAYOUT = "wide"
    
    # Default Admin User
    DEFAULT_ADMIN_USERNAME = os.getenv("DEFAULT_ADMIN_USERNAME", "admin")
    DEFAULT_ADMIN_PASSWORD = os.getenv("DEFAULT_ADMIN_PASSWORD", "admin123")
    DEFAULT_ADMIN_EMAIL = os.getenv("DEFAULT_ADMIN_EMAIL", "<EMAIL>")

settings = Settings()
