#!/bin/bash

# Service Management Portal Restore Script

set -e

if [ $# -eq 0 ]; then
    echo "❌ Usage: $0 <backup_file>"
    echo "   Example: $0 backups/service_portal_backup_20231201_120000.tar.gz"
    exit 1
fi

BACKUP_FILE="$1"

if [ ! -f "$BACKUP_FILE" ]; then
    echo "❌ Backup file not found: $BACKUP_FILE"
    exit 1
fi

echo "🔄 Starting restore process..."
echo "📦 Backup file: $BACKUP_FILE"

# Create temporary directory for extraction
TEMP_DIR=$(mktemp -d)
echo "📁 Using temporary directory: $TEMP_DIR"

# Extract backup
echo "📂 Extracting backup..."
tar -xzf "$BACKUP_FILE" -C "$TEMP_DIR"

# Stop services
echo "⏹️  Stopping services..."
docker-compose down

# Restore configuration files
echo "📝 Restoring configuration files..."
if [ -f "$TEMP_DIR/.env" ]; then
    cp "$TEMP_DIR/.env" .
    echo "✅ .env file restored"
fi

if [ -d "$TEMP_DIR/ssh_keys" ]; then
    rm -rf ssh_keys
    cp -r "$TEMP_DIR/ssh_keys" .
    chmod 700 ssh_keys
    echo "✅ SSH keys restored"
fi

if [ -d "$TEMP_DIR/logs" ]; then
    rm -rf logs
    cp -r "$TEMP_DIR/logs" .
    echo "✅ Logs restored"
fi

# Start database service
echo "🗄️  Starting database service..."
docker-compose up -d db

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 15

# Restore database
echo "📊 Restoring database..."
if [ -f "$TEMP_DIR/database_backup.sql" ]; then
    # Drop and recreate database
    docker-compose exec -T db psql -U postgres -c "DROP DATABASE IF EXISTS service_portal;"
    docker-compose exec -T db psql -U postgres -c "CREATE DATABASE service_portal;"
    
    # Restore database
    docker-compose exec -T db psql -U postgres service_portal < "$TEMP_DIR/database_backup.sql"
    echo "✅ Database restored"
else
    echo "⚠️  No database backup found, initializing new database..."
    docker-compose exec portal python database/init_db.py
fi

# Start all services
echo "🚀 Starting all services..."
docker-compose up -d

# Clean up temporary directory
rm -rf "$TEMP_DIR"

echo "✅ Restore completed successfully!"
echo "🌐 Access the Service Management Portal at: http://localhost:8501"
