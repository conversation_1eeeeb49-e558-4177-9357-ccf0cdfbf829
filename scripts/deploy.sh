#!/bin/bash

# Service Management Portal Deployment Script

set -e

echo "🚀 Starting Service Management Portal Deployment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p ssh_keys logs

# Set proper permissions for SSH keys directory
chmod 700 ssh_keys

# Copy environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration before continuing."
    echo "   Press Enter to continue after editing .env file..."
    read
fi

# Build and start services
echo "🔨 Building and starting services..."
docker-compose down --remove-orphans
docker-compose build --no-cache
docker-compose up -d

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Initialize database
echo "🗄️  Initializing database..."
docker-compose exec portal python database/init_db.py

# Show status
echo "📊 Checking service status..."
docker-compose ps

echo ""
echo "✅ Deployment completed successfully!"
echo ""
echo "🌐 Access the Service Management Portal at: http://localhost:8501"
echo "🔐 Default login credentials:"
echo "   Username: admin"
echo "   Password: admin123"
echo ""
echo "📊 Additional services:"
echo "   - Demo Nginx: http://localhost:8080"
echo "   - Demo Apache: http://localhost:8081"
echo "   - Portainer: http://localhost:9000"
echo "   - PostgreSQL: localhost:5432"
echo ""
echo "⚠️  Remember to change the default admin password!"
echo ""
echo "📋 Useful commands:"
echo "   - View logs: docker-compose logs -f portal"
echo "   - Stop services: docker-compose down"
echo "   - Restart services: docker-compose restart"
echo "   - Update services: docker-compose pull && docker-compose up -d"
