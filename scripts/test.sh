#!/bin/bash

# Service Management Portal Test Script

set -e

echo "🧪 Starting Service Management Portal Tests..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test functions
test_service() {
    local service_name=$1
    local port=$2
    local path=${3:-""}
    
    echo -n "Testing $service_name on port $port... "
    
    if curl -s -f "http://localhost:$port$path" > /dev/null; then
        echo -e "${GREEN}✅ PASS${NC}"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC}"
        return 1
    fi
}

test_docker_service() {
    local container_name=$1
    
    echo -n "Testing Docker container $container_name... "
    
    if docker-compose ps | grep -q "$container_name.*Up"; then
        echo -e "${GREEN}✅ RUNNING${NC}"
        return 0
    else
        echo -e "${RED}❌ NOT RUNNING${NC}"
        return 1
    fi
}

# Check if Docker Compose is running
echo "🐳 Checking Docker Compose services..."
if ! docker-compose ps > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker Compose services are not running${NC}"
    echo "Please run: docker-compose up -d"
    exit 1
fi

# Test Docker containers
echo ""
echo "📦 Testing Docker containers..."
test_docker_service "service_portal_app"
test_docker_service "service_portal_db"
test_docker_service "demo_nginx"
test_docker_service "demo_apache"
test_docker_service "demo_mysql"
test_docker_service "demo_redis"
test_docker_service "portainer"

# Test web services
echo ""
echo "🌐 Testing web services..."
test_service "Service Portal" "8501" "/_stcore/health"
test_service "Demo Nginx" "8080"
test_service "Demo Apache" "8081"
test_service "Portainer" "9000"

# Test database connection
echo ""
echo "🗄️  Testing database connection..."
echo -n "Testing PostgreSQL connection... "
if docker-compose exec -T db pg_isready -U postgres > /dev/null 2>&1; then
    echo -e "${GREEN}✅ CONNECTED${NC}"
else
    echo -e "${RED}❌ CONNECTION FAILED${NC}"
fi

# Test Redis connection
echo -n "Testing Redis connection... "
if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
    echo -e "${GREEN}✅ CONNECTED${NC}"
else
    echo -e "${RED}❌ CONNECTION FAILED${NC}"
fi

# Test MySQL connection
echo -n "Testing MySQL connection... "
if docker-compose exec -T mysql mysqladmin ping -h localhost -u root -prootpassword > /dev/null 2>&1; then
    echo -e "${GREEN}✅ CONNECTED${NC}"
else
    echo -e "${RED}❌ CONNECTION FAILED${NC}"
fi

# Test portal functionality
echo ""
echo "🔧 Testing portal functionality..."

# Test if portal can connect to Docker
echo -n "Testing Docker socket access... "
if docker-compose exec -T portal python -c "
import docker
try:
    client = docker.from_env()
    client.ping()
    print('SUCCESS')
except Exception as e:
    print(f'FAILED: {e}')
    exit(1)
" | grep -q "SUCCESS"; then
    echo -e "${GREEN}✅ ACCESSIBLE${NC}"
else
    echo -e "${RED}❌ NOT ACCESSIBLE${NC}"
fi

# Test database initialization
echo -n "Testing database tables... "
if docker-compose exec -T db psql -U postgres service_portal -c "\dt" | grep -q "users"; then
    echo -e "${GREEN}✅ INITIALIZED${NC}"
else
    echo -e "${YELLOW}⚠️  NOT INITIALIZED${NC}"
    echo "Run: docker-compose exec portal python database/init_db.py"
fi

# Summary
echo ""
echo "📊 Test Summary:"
echo "=================="

# Count running containers
running_containers=$(docker-compose ps | grep "Up" | wc -l)
total_containers=$(docker-compose ps | tail -n +3 | wc -l)

echo "Docker containers: $running_containers/$total_containers running"

# Test web endpoints
web_tests=0
web_passed=0

for port in 8501 8080 8081 9000; do
    web_tests=$((web_tests + 1))
    if curl -s -f "http://localhost:$port" > /dev/null 2>&1; then
        web_passed=$((web_passed + 1))
    fi
done

echo "Web services: $web_passed/$web_tests accessible"

# Overall status
if [ $running_containers -eq $total_containers ] && [ $web_passed -eq $web_tests ]; then
    echo -e "${GREEN}🎉 All tests passed! Service Management Portal is ready.${NC}"
    echo ""
    echo "Access the portal at: http://localhost:8501"
    echo "Default login: admin / admin123"
    exit 0
else
    echo -e "${RED}❌ Some tests failed. Please check the logs.${NC}"
    echo ""
    echo "Useful commands:"
    echo "  docker-compose logs -f portal"
    echo "  docker-compose ps"
    echo "  docker-compose restart"
    exit 1
fi
