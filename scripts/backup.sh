#!/bin/bash

# Service Management Portal Backup Script

set -e

BACKUP_DIR="backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="service_portal_backup_${TIMESTAMP}.tar.gz"

echo "💾 Starting backup process..."

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Create temporary directory for backup
TEMP_DIR=$(mktemp -d)
echo "📁 Using temporary directory: $TEMP_DIR"

# Backup database
echo "🗄️  Backing up database..."
docker-compose exec -T db pg_dump -U postgres service_portal > "$TEMP_DIR/database_backup.sql"

# Backup configuration files
echo "📝 Backing up configuration files..."
cp .env "$TEMP_DIR/" 2>/dev/null || echo "No .env file found"
cp -r ssh_keys "$TEMP_DIR/" 2>/dev/null || echo "No SSH keys found"

# Backup logs
echo "📋 Backing up logs..."
cp -r logs "$TEMP_DIR/" 2>/dev/null || echo "No logs found"

# Create compressed archive
echo "🗜️  Creating compressed archive..."
cd "$TEMP_DIR"
tar -czf "$BACKUP_FILE" *
cd - > /dev/null

# Move backup to backup directory
mv "$TEMP_DIR/$BACKUP_FILE" "$BACKUP_DIR/"

# Clean up temporary directory
rm -rf "$TEMP_DIR"

echo "✅ Backup completed successfully!"
echo "📦 Backup file: $BACKUP_DIR/$BACKUP_FILE"

# Keep only last 10 backups
echo "🧹 Cleaning up old backups (keeping last 10)..."
cd "$BACKUP_DIR"
ls -t service_portal_backup_*.tar.gz | tail -n +11 | xargs -r rm
cd - > /dev/null

echo "💾 Backup process finished!"
