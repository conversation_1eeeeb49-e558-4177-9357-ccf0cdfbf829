import streamlit as st
from services.cron_manager import cron_job_manager
from ui.components import show_action_result
from auth.authentication import auth_manager
from database.init_db import get_db_session
from database.models import CronJob, Server

def show_cron_jobs():
    """Display cron jobs management page"""
    st.title("⏰ Cron Jobs Management")
    
    # Refresh button
    col1, col2, col3 = st.columns([1, 1, 4])
    with col1:
        if st.button("🔄 Refresh", use_container_width=True):
            st.rerun()
    
    with col2:
        if st.button("➕ Add Cron Job", use_container_width=True):
            st.session_state.show_add_cron_form = True
    
    # Show add cron job form if requested
    if st.session_state.get('show_add_cron_form', False):
        show_add_cron_job_form()
    
    # Get all cron jobs status
    with st.spinner("Loading cron jobs..."):
        cron_jobs_status = cron_job_manager.get_all_cron_jobs_status()
    
    if not cron_jobs_status:
        st.warning("No cron jobs available or accessible to your account.")
        return
    
    # Display cron jobs summary
    col1, col2, col3, col4 = st.columns(4)
    
    enabled_count = sum(1 for c in cron_jobs_status if c['status'] == 'enabled')
    disabled_count = sum(1 for c in cron_jobs_status if c['status'] == 'disabled')
    not_found_count = sum(1 for c in cron_jobs_status if c['status'] == 'not_found')
    total_count = len(cron_jobs_status)
    
    with col1:
        st.metric("Total Cron Jobs", total_count)
    with col2:
        st.metric("Enabled", enabled_count, delta=None)
    with col3:
        st.metric("Disabled", disabled_count, delta=None)
    with col4:
        st.metric("Not Found", not_found_count, delta=None)
    
    st.markdown("---")
    
    # Display cron jobs
    st.subheader("⏰ Scheduled Jobs")
    
    # Handle cron job actions
    action_performed = False
    
    for cron_job_info in cron_jobs_status:
        action, cron_job_id = show_cron_job_card(cron_job_info)
        
        if action and cron_job_id:
            action_performed = True
            
            if action == 'enable':
                with st.spinner(f"Enabling {cron_job_info['name']}..."):
                    success, message = cron_job_manager.enable_cron_job(cron_job_id)
                show_action_result(success, message, "Enable")
            
            elif action == 'disable':
                with st.spinner(f"Disabling {cron_job_info['name']}..."):
                    success, message = cron_job_manager.disable_cron_job(cron_job_id)
                show_action_result(success, message, "Disable")
            
            elif action == 'delete':
                # Show confirmation dialog
                if st.session_state.get(f'confirm_delete_{cron_job_id}', False):
                    with st.spinner(f"Deleting {cron_job_info['name']}..."):
                        success, message = cron_job_manager.delete_cron_job(cron_job_id)
                    show_action_result(success, message, "Delete")
                    if f'confirm_delete_{cron_job_id}' in st.session_state:
                        del st.session_state[f'confirm_delete_{cron_job_id}']
                else:
                    st.session_state[f'confirm_delete_{cron_job_id}'] = True
                    st.warning(f"Click delete again to confirm deletion of '{cron_job_info['name']}'")
            
            elif action == 'details':
                st.session_state.selected_cron_job_details = cron_job_id
                st.session_state.page = 'cron_details'
                st.rerun()

def show_cron_job_card(cron_job_info):
    """Display a cron job card with status and controls"""
    cron_job_id = cron_job_info['id']
    cron_job_name = cron_job_info['name']
    server_name = cron_job_info['server']
    schedule = cron_job_info['schedule']
    command = cron_job_info['command']
    user = cron_job_info['user']
    description = cron_job_info['description']
    status = cron_job_info['status']
    permissions = cron_job_info['permissions']
    
    # Determine status color and icon
    if status == 'enabled':
        status_color = "🟢"
        status_text = "Enabled"
        status_style = "color: green;"
    elif status == 'disabled':
        status_color = "🟡"
        status_text = "Disabled"
        status_style = "color: orange;"
    elif status == 'not_found':
        status_color = "🔴"
        status_text = "Not Found"
        status_style = "color: red;"
    else:
        status_color = "🟡"
        status_text = "Unknown"
        status_style = "color: orange;"
    
    # Create cron job card
    with st.container():
        st.markdown(f"""
        <div style="border: 1px solid #ddd; border-radius: 10px; padding: 15px; margin: 10px 0; background-color: #f9f9f9;">
            <h4 style="margin: 0 0 10px 0;">{status_color} {cron_job_name}</h4>
            <p style="margin: 5px 0; color: #666;"><strong>Server:</strong> {server_name}</p>
            <p style="margin: 5px 0; color: #666;"><strong>Schedule:</strong> <code>{schedule}</code></p>
            <p style="margin: 5px 0; color: #666;"><strong>Command:</strong> <code>{command}</code></p>
            <p style="margin: 5px 0; color: #666;"><strong>User:</strong> {user}</p>
            <p style="margin: 5px 0; color: #666;"><strong>Description:</strong> {description}</p>
            <p style="margin: 5px 0; {status_style}"><strong>Status:</strong> {status_text}</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Control buttons
        col1, col2, col3, col4, col5 = st.columns([1, 1, 1, 1, 2])
        
        with col1:
            if permissions['can_enable_disable'] and status == 'disabled':
                if st.button("✅ Enable", key=f"enable_{cron_job_id}"):
                    return 'enable', cron_job_id
        
        with col2:
            if permissions['can_enable_disable'] and status == 'enabled':
                if st.button("⏸️ Disable", key=f"disable_{cron_job_id}"):
                    return 'disable', cron_job_id
        
        with col3:
            if permissions['can_delete']:
                if st.button("🗑️ Delete", key=f"delete_{cron_job_id}"):
                    return 'delete', cron_job_id
        
        with col4:
            if permissions['can_view']:
                if st.button("📊 Details", key=f"details_{cron_job_id}"):
                    return 'details', cron_job_id
    
    return None, None

def show_add_cron_job_form():
    """Display form to add new cron job"""
    st.subheader("➕ Add New Cron Job")
    
    with st.form("add_cron_job_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            name = st.text_input("Job Name", placeholder="e.g., Daily Backup")
            
            # Get available servers
            session = get_db_session()
            try:
                servers = session.query(Server).filter_by(is_active=True).all()
                server_options = {f"{s.name} ({s.hostname})": s.id for s in servers}
                
                if server_options:
                    selected_server = st.selectbox("Server", list(server_options.keys()))
                    server_id = server_options[selected_server]
                else:
                    st.error("No servers available. Please add a server first.")
                    return
            finally:
                session.close()
            
            user = st.text_input("User", value="root", placeholder="User to run the job as")
        
        with col2:
            schedule = st.text_input("Schedule (Cron Format)", placeholder="0 2 * * * (daily at 2 AM)")
            command = st.text_area("Command", placeholder="Command to execute")
            description = st.text_area("Description", placeholder="Optional description")
        
        # Cron schedule help
        with st.expander("Cron Schedule Format Help"):
            st.markdown("""
            **Cron format:** `minute hour day month weekday`
            
            **Examples:**
            - `0 2 * * *` - Daily at 2:00 AM
            - `0 */6 * * *` - Every 6 hours
            - `30 1 * * 0` - Weekly on Sunday at 1:30 AM
            - `0 0 1 * *` - Monthly on the 1st at midnight
            - `*/15 * * * *` - Every 15 minutes
            
            **Special characters:**
            - `*` - Any value
            - `*/n` - Every n units
            - `n-m` - Range from n to m
            - `n,m` - List of values
            """)
        
        submitted = st.form_submit_button("Create Cron Job")
        cancel = st.form_submit_button("Cancel")
        
        if cancel:
            st.session_state.show_add_cron_form = False
            st.rerun()
        
        if submitted:
            if name and schedule and command:
                with st.spinner("Creating cron job..."):
                    success, message = cron_job_manager.create_cron_job(
                        name=name,
                        server_id=server_id,
                        schedule=schedule,
                        command=command,
                        user=user,
                        description=description
                    )
                
                if success:
                    st.success(message)
                    st.session_state.show_add_cron_form = False
                    st.rerun()
                else:
                    st.error(message)
            else:
                st.error("Please fill in all required fields (Name, Schedule, Command)")

def show_cron_job_details():
    """Display detailed cron job information"""
    if 'selected_cron_job_details' not in st.session_state:
        st.error("No cron job selected")
        return
    
    cron_job_id = st.session_state.selected_cron_job_details
    
    # Back button
    if st.button("← Back to Cron Jobs"):
        st.session_state.page = 'cron_jobs'
        st.rerun()
    
    # Get cron job details
    success, status, cron_job = cron_job_manager.get_cron_job_status(cron_job_id)
    
    if not success or not cron_job:
        st.error("Cron job not found or inaccessible")
        return
    
    st.title(f"📊 {cron_job.name} - Cron Job Details")
    
    # Cron job information
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Cron Job Information")
        st.write(f"**Name:** {cron_job.name}")
        st.write(f"**Linux Server:** {cron_job.server.name}")
        st.write(f"**Schedule:** `{cron_job.schedule}`")
        st.write(f"**Command:** `{cron_job.command}`")
        st.write(f"**User:** {cron_job.user}")
        st.write(f"**Description:** {cron_job.description}")
        st.write(f"**Created:** {cron_job.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
    
    with col2:
        st.subheader("Current Status")
        if status == 'enabled':
            st.success("🟢 Enabled")
        elif status == 'disabled':
            st.warning("🟡 Disabled")
        elif status == 'not_found':
            st.error("🔴 Not Found in Crontab")
        else:
            st.warning("🟡 Unknown")
        
        # Control buttons
        button_col1, button_col2, button_col3 = st.columns(3)
        
        with button_col1:
            if status == 'disabled' and st.button("✅ Enable Cron Job"):
                with st.spinner("Enabling cron job..."):
                    success, message = cron_job_manager.enable_cron_job(cron_job_id)
                show_action_result(success, message, "Enable")
                st.rerun()
        
        with button_col2:
            if status == 'enabled' and st.button("⏸️ Disable Cron Job"):
                with st.spinner("Disabling cron job..."):
                    success, message = cron_job_manager.disable_cron_job(cron_job_id)
                show_action_result(success, message, "Disable")
                st.rerun()
        
        with button_col3:
            if st.button("🗑️ Delete Cron Job"):
                if st.session_state.get('confirm_delete_details', False):
                    with st.spinner("Deleting cron job..."):
                        success, message = cron_job_manager.delete_cron_job(cron_job_id)
                    show_action_result(success, message, "Delete")
                    if success:
                        st.session_state.page = 'cron_jobs'
                        st.rerun()
                else:
                    st.session_state.confirm_delete_details = True
                    st.warning("Click delete again to confirm")
