import streamlit as st
from datetime import datetime

def show_service_card(service_info):
    """Display a service card with status and controls"""
    service_id = service_info['id']
    service_name = service_info['name']
    server_name = service_info['server']
    description = service_info['description']
    status = service_info['status']
    permissions = service_info['permissions']
    
    # Determine status color and icon
    if status == 'active':
        status_color = "🟢"
        status_text = "Running"
        status_style = "color: green;"
    elif status == 'inactive':
        status_color = "🔴"
        status_text = "Stopped"
        status_style = "color: red;"
    else:
        status_color = "🟡"
        status_text = "Unknown"
        status_style = "color: orange;"
    
    # Create service card
    with st.container():
        st.markdown(f"""
        <div style="border: 1px solid #ddd; border-radius: 10px; padding: 15px; margin: 10px 0; background-color: #f9f9f9;">
            <h4 style="margin: 0 0 10px 0;">{status_color} {service_name}</h4>
            <p style="margin: 5px 0; color: #666;"><strong>Server:</strong> {server_name}</p>
            <p style="margin: 5px 0; color: #666;"><strong>Description:</strong> {description}</p>
            <p style="margin: 5px 0; {status_style}"><strong>Status:</strong> {status_text}</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Control buttons
        col1, col2, col3, col4, col5 = st.columns([1, 1, 1, 1, 2])
        
        with col1:
            if permissions['can_start']:
                if st.button("▶️ Start", key=f"start_{service_id}", disabled=(status == 'active')):
                    return 'start', service_id
        
        with col2:
            if permissions['can_stop']:
                if st.button("⏹️ Stop", key=f"stop_{service_id}", disabled=(status != 'active')):
                    return 'stop', service_id
        
        with col3:
            if permissions['can_restart']:
                if st.button("🔄 Restart", key=f"restart_{service_id}"):
                    return 'restart', service_id
        
        with col4:
            if permissions['can_view']:
                if st.button("📋 Logs", key=f"logs_{service_id}"):
                    return 'logs', service_id
        
        with col5:
            if permissions['can_view']:
                if st.button("📊 Details", key=f"details_{service_id}"):
                    return 'details', service_id
    
    return None, None

def show_status_badge(status):
    """Display status badge"""
    if status == 'active':
        st.success("🟢 Running")
    elif status == 'inactive':
        st.error("🔴 Stopped")
    else:
        st.warning("🟡 Unknown")

def show_user_info():
    """Display current user information in sidebar"""
    if 'username' in st.session_state:
        st.sidebar.markdown("---")
        st.sidebar.markdown("**Current User:**")
        st.sidebar.write(f"👤 {st.session_state.username}")
        
        if 'user_roles' in st.session_state:
            roles = ", ".join(st.session_state.user_roles)
            st.sidebar.write(f"🏷️ {roles}")
        
        if 'login_time' in st.session_state:
            login_time = st.session_state.login_time
            st.sidebar.write(f"🕐 {login_time.strftime('%H:%M:%S')}")

def show_navigation():
    """Display navigation menu"""
    st.sidebar.title("🔧 Service Portal")
    
    # Navigation options
    pages = {
        "Dashboard": "🏠",
        "Service Details": "📊",
        "Audit Logs": "📝",
        "Settings": "⚙️"
    }
    
    selected_page = st.sidebar.selectbox(
        "Navigate to:",
        list(pages.keys()),
        format_func=lambda x: f"{pages[x]} {x}"
    )
    
    return selected_page

def show_action_result(success, message, action_type="Action"):
    """Display action result with appropriate styling"""
    if success:
        st.success(f"✅ {action_type} completed successfully: {message}")
    else:
        st.error(f"❌ {action_type} failed: {message}")

def show_loading_spinner(text="Loading..."):
    """Display loading spinner with custom text"""
    return st.spinner(text)

def show_confirmation_dialog(action, service_name):
    """Show confirmation dialog for destructive actions"""
    if action in ['stop', 'restart']:
        return st.checkbox(
            f"I confirm I want to {action} the {service_name} service",
            key=f"confirm_{action}_{service_name}"
        )
    return True

def format_timestamp(timestamp):
    """Format timestamp for display"""
    if isinstance(timestamp, datetime):
        return timestamp.strftime("%Y-%m-%d %H:%M:%S")
    return str(timestamp)

def show_service_metrics(service_info):
    """Display service metrics in a nice layout"""
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Status", service_info.get('status', 'Unknown'))
    
    with col2:
        st.metric("Server", service_info.get('server', 'Unknown'))
    
    with col3:
        uptime = service_info.get('uptime', 'Unknown')
        st.metric("Uptime", uptime)
    
    with col4:
        memory = service_info.get('memory_usage', 'Unknown')
        st.metric("Memory", memory)
