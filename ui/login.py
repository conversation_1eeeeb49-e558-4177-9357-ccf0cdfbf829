import streamlit as st
from auth.authentication import auth_manager
from config.settings import settings

def show_login_page():
    """Display login page"""
    st.set_page_config(
        page_title=settings.PAGE_TITLE,
        page_icon=settings.PAGE_ICON,
        layout="centered"
    )
    
    # Center the login form
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.title("🐧 Linux System Services & Cron Jobs Portal")
        st.markdown("---")
        
        # Login form
        with st.form("login_form"):
            st.subheader("Login")
            
            username = st.text_input("Username", placeholder="Enter your username")
            password = st.text_input("Password", type="password", placeholder="Enter your password")
            
            submit_button = st.form_submit_button("Login", use_container_width=True)
            
            if submit_button:
                if username and password:
                    success, message = auth_manager.login(username, password)
                    if success:
                        st.success(message)
                        st.rerun()
                    else:
                        st.error(message)
                else:
                    st.error("Please enter both username and password")
        
        # Default credentials info
        st.markdown("---")
        with st.expander("Default Credentials", expanded=False):
            st.info(f"""
            **Default Admin Account:**
            - Username: `{settings.DEFAULT_ADMIN_USERNAME}`
            - Password: `{settings.DEFAULT_ADMIN_PASSWORD}`
            
            *Please change the default password after first login*
            """)
        
        # Application info
        st.markdown("---")
        st.markdown(f"""
        <div style="text-align: center; color: #666;">
            <small>{settings.APP_NAME} v{settings.APP_VERSION}</small>
        </div>
        """, unsafe_allow_html=True)
