import streamlit as st
from services.service_manager import service_manager
from services.log_viewer import log_viewer
from ui.components import show_service_card, show_user_info, show_navigation, show_action_result
from auth.authentication import auth_manager
from database.init_db import get_db_session
from database.models import AuditLog, User, Service
from datetime import datetime, timed<PERSON><PERSON>

def show_dashboard():
    """Display main dashboard"""
    st.title("🏠 Linux System Services & Cron Jobs Dashboard")
    
    # Refresh button
    col1, col2, col3 = st.columns([1, 1, 4])
    with col1:
        if st.button("🔄 Refresh", use_container_width=True):
            st.rerun()
    
    with col2:
        auto_refresh = st.checkbox("Auto-refresh (30s)")
    
    # Get all services status
    with st.spinner("Loading system services..."):
        services_status = service_manager.get_all_services_status()

    if not services_status:
        st.warning("No system services available or accessible to your account.")
        return
    
    # Display services summary
    col1, col2, col3, col4 = st.columns(4)
    
    running_count = sum(1 for s in services_status if s['status'] == 'active')
    stopped_count = sum(1 for s in services_status if s['status'] == 'inactive')
    unknown_count = sum(1 for s in services_status if s['status'] not in ['active', 'inactive'])
    total_count = len(services_status)
    
    with col1:
        st.metric("Total System Services", total_count)
    with col2:
        st.metric("Running", running_count, delta=None)
    with col3:
        st.metric("Stopped", stopped_count, delta=None)
    with col4:
        st.metric("Unknown", unknown_count, delta=None)

    st.markdown("---")

    # Display services
    st.subheader("🔧 Linux System Services")

    # Add quick link to cron jobs
    col1, col2 = st.columns([3, 1])
    with col2:
        if st.button("⏰ Manage Cron Jobs", use_container_width=True):
            st.session_state.page = 'cron_jobs'
            st.rerun()

    # Handle service actions
    action_performed = False

    for service_info in services_status:
        action, service_id = show_service_card(service_info)
        
        if action and service_id:
            action_performed = True
            
            if action == 'start':
                with st.spinner(f"Starting {service_info['name']}..."):
                    success, message = service_manager.start_service(service_id)
                show_action_result(success, message, "Start")
            
            elif action == 'stop':
                with st.spinner(f"Stopping {service_info['name']}..."):
                    success, message = service_manager.stop_service(service_id)
                show_action_result(success, message, "Stop")
            
            elif action == 'restart':
                with st.spinner(f"Restarting {service_info['name']}..."):
                    success, message = service_manager.restart_service(service_id)
                show_action_result(success, message, "Restart")
            
            elif action == 'logs':
                st.session_state.selected_service_logs = service_id
                st.session_state.page = 'logs'
                st.rerun()
            
            elif action == 'details':
                st.session_state.selected_service_details = service_id
                st.session_state.page = 'details'
                st.rerun()
    
    # Add cron jobs summary section
    st.markdown("---")
    st.subheader("⏰ Cron Jobs Summary")

    # Get cron jobs status
    from services.cron_manager import cron_job_manager
    try:
        cron_jobs_status = cron_job_manager.get_all_cron_jobs_status()

        if cron_jobs_status:
            col1, col2, col3, col4 = st.columns(4)

            total_cron = len(cron_jobs_status)
            enabled_cron = sum(1 for c in cron_jobs_status if c['status'] == 'enabled')
            disabled_cron = sum(1 for c in cron_jobs_status if c['status'] == 'disabled')
            not_found_cron = sum(1 for c in cron_jobs_status if c['status'] == 'not_found')

            with col1:
                st.metric("Total Cron Jobs", total_cron)
            with col2:
                st.metric("Enabled", enabled_cron)
            with col3:
                st.metric("Disabled", disabled_cron)
            with col4:
                st.metric("Issues", not_found_cron)

            if st.button("⏰ View All Cron Jobs", use_container_width=True):
                st.session_state.page = 'cron_jobs'
                st.rerun()
        else:
            st.info("No cron jobs configured. Click 'Manage Cron Jobs' to add some.")
    except Exception as e:
        st.warning(f"Could not load cron jobs summary: {str(e)}")

    # Auto-refresh functionality
    if auto_refresh and not action_performed:
        import time
        time.sleep(30)
        st.rerun()

def show_service_details():
    """Display detailed service information"""
    if 'selected_service_details' not in st.session_state:
        st.error("No service selected")
        return
    
    service_id = st.session_state.selected_service_details
    
    # Back button
    if st.button("← Back to Dashboard"):
        st.session_state.page = 'dashboard'
        st.rerun()
    
    # Get service details
    success, status, service = service_manager.get_service_status(service_id)
    
    if not success or not service:
        st.error("Service not found or inaccessible")
        return
    
    st.title(f"📊 {service.name} - System Service Details")

    # Service information
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("System Service Information")
        st.write(f"**Service Name:** {service.name}")
        st.write(f"**Linux Server:** {service.server.name}")
        st.write(f"**Description:** {service.description}")
        st.write(f"**Systemd Service:** {service.service_command}")
        st.write(f"**Log Path:** {service.log_path or 'Uses journalctl'}")
    
    with col2:
        st.subheader("Current Status")
        if status == 'active':
            st.success("🟢 Running")
        elif status == 'inactive':
            st.error("🔴 Stopped")
        else:
            st.warning("🟡 Unknown")
        
        # Control buttons
        from auth.authorization import auth_manager_auth
        permissions = auth_manager_auth.get_service_permissions(service_id)
        
        button_col1, button_col2, button_col3 = st.columns(3)
        
        with button_col1:
            if permissions['can_start'] and st.button("▶️ Start System Service"):
                with st.spinner("Starting system service..."):
                    success, message = service_manager.start_service(service_id)
                show_action_result(success, message, "Start")
                st.rerun()

        with button_col2:
            if permissions['can_stop'] and st.button("⏹️ Stop System Service"):
                with st.spinner("Stopping system service..."):
                    success, message = service_manager.stop_service(service_id)
                show_action_result(success, message, "Stop")
                st.rerun()

        with button_col3:
            if permissions['can_restart'] and st.button("🔄 Restart System Service"):
                with st.spinner("Restarting system service..."):
                    success, message = service_manager.restart_service(service_id)
                show_action_result(success, message, "Restart")
                st.rerun()
    
    st.markdown("---")
    
    # Logs section
    if permissions['can_view']:
        st.subheader("System Service Logs")
        log_viewer.display_logs(service_id)

def show_service_logs():
    """Display service logs page"""
    if 'selected_service_logs' not in st.session_state:
        st.error("No service selected")
        return
    
    service_id = st.session_state.selected_service_logs
    
    # Back button
    if st.button("← Back to Dashboard"):
        st.session_state.page = 'dashboard'
        st.rerun()
    
    # Get service info
    session = get_db_session()
    try:
        service = session.query(Service).filter_by(id=service_id).first()
        if not service:
            st.error("Service not found")
            return
        
        st.title(f"📋 {service.name} - Logs")
        
        # Display logs with all features
        log_viewer.display_logs(service_id, auto_refresh=False)
        
        st.markdown("---")
        
        # Log search functionality
        log_viewer.display_log_search(service_id)
        
    finally:
        session.close()

def show_audit_logs():
    """Display audit logs"""
    st.title("📝 Audit Logs")
    
    # Filters
    col1, col2, col3 = st.columns(3)
    
    with col1:
        days_back = st.selectbox("Show logs from:", [1, 7, 30, 90], index=1)
    
    with col2:
        action_filter = st.selectbox(
            "Action:",
            ["All", "start", "stop", "restart", "view_logs"]
        )
    
    with col3:
        result_filter = st.selectbox("Result:", ["All", "success", "failed"])
    
    # Get audit logs
    session = get_db_session()
    try:
        query = session.query(AuditLog).join(User).join(Service)
        
        # Apply filters
        since_date = datetime.now() - timedelta(days=days_back)
        query = query.filter(AuditLog.timestamp >= since_date)
        
        if action_filter != "All":
            query = query.filter(AuditLog.action == action_filter)
        
        if result_filter != "All":
            query = query.filter(AuditLog.result == result_filter)
        
        # Order by timestamp descending
        audit_logs = query.order_by(AuditLog.timestamp.desc()).limit(100).all()
        
        if audit_logs:
            # Display logs in a table
            log_data = []
            for log in audit_logs:
                log_data.append({
                    "Timestamp": log.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                    "User": log.user.username,
                    "Service": log.service.name,
                    "Action": log.action,
                    "Result": log.result,
                    "Details": log.details or ""
                })
            
            st.dataframe(log_data, use_container_width=True)
        else:
            st.info("No audit logs found for the selected criteria.")
    
    finally:
        session.close()
