from database.init_db import get_db_session
from database.models import User, Service, ServicePermission
from auth.authentication import auth_manager

class AuthorizationManager:
    def __init__(self):
        pass
    
    def can_view_service(self, service_id):
        """Check if current user can view service"""
        user = auth_manager.get_current_user()
        if not user:
            return False
        
        # Admin can view all services
        if auth_manager.is_admin():
            return True
        
        return user.has_permission(service_id, 'can_view')
    
    def can_start_service(self, service_id):
        """Check if current user can start service"""
        user = auth_manager.get_current_user()
        if not user:
            return False
        
        # Admin can start all services
        if auth_manager.is_admin():
            return True
        
        return user.has_permission(service_id, 'can_start')
    
    def can_stop_service(self, service_id):
        """Check if current user can stop service"""
        user = auth_manager.get_current_user()
        if not user:
            return False
        
        # Admin can stop all services
        if auth_manager.is_admin():
            return True
        
        return user.has_permission(service_id, 'can_stop')
    
    def can_restart_service(self, service_id):
        """Check if current user can restart service"""
        user = auth_manager.get_current_user()
        if not user:
            return False
        
        # Admin can restart all services
        if auth_manager.is_admin():
            return True
        
        return user.has_permission(service_id, 'can_restart')
    
    def get_accessible_services(self):
        """Get list of services accessible to current user"""
        user = auth_manager.get_current_user()
        if not user:
            return []
        
        session = get_db_session()
        try:
            if auth_manager.is_admin():
                # Admin can access all active services
                services = session.query(Service).filter_by(is_active=True).all()
            else:
                # Get services based on permissions
                services = []
                for role in user.roles:
                    for perm in role.service_permissions:
                        if perm.can_view and perm.service.is_active:
                            services.append(perm.service)
                
                # Remove duplicates
                services = list(set(services))
            
            return services
        finally:
            session.close()
    
    def get_service_permissions(self, service_id):
        """Get current user's permissions for a specific service"""
        return {
            'can_view': self.can_view_service(service_id),
            'can_start': self.can_start_service(service_id),
            'can_stop': self.can_stop_service(service_id),
            'can_restart': self.can_restart_service(service_id)
        }

# Global authorization manager instance
auth_manager_auth = AuthorizationManager()
