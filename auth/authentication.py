import streamlit as st
from datetime import datetime, timedelta
from database.init_db import get_db_session
from database.models import User
from config.settings import settings

class AuthenticationManager:
    def __init__(self):
        self.session_timeout = settings.SESSION_TIMEOUT
    
    def login(self, username, password):
        """Authenticate user and create session"""
        session = get_db_session()
        try:
            user = session.query(User).filter_by(username=username, is_active=True).first()
            
            if user and user.check_password(password):
                # Create session
                st.session_state.authenticated = True
                st.session_state.user_id = user.id
                st.session_state.username = user.username
                st.session_state.user_roles = [role.name for role in user.roles]
                st.session_state.login_time = datetime.now()
                return True, "Login successful"
            else:
                return False, "Invalid username or password"
        except Exception as e:
            return False, f"Login error: {str(e)}"
        finally:
            session.close()
    
    def logout(self):
        """Clear session and logout user"""
        for key in ['authenticated', 'user_id', 'username', 'user_roles', 'login_time']:
            if key in st.session_state:
                del st.session_state[key]
    
    def is_authenticated(self):
        """Check if user is authenticated and session is valid"""
        if not st.session_state.get('authenticated', False):
            return False
        
        # Check session timeout
        login_time = st.session_state.get('login_time')
        if login_time:
            if datetime.now() - login_time > timedelta(seconds=self.session_timeout):
                self.logout()
                return False
        
        return True
    
    def get_current_user(self):
        """Get current authenticated user"""
        if not self.is_authenticated():
            return None
        
        session = get_db_session()
        try:
            user = session.query(User).filter_by(id=st.session_state.user_id).first()
            return user
        finally:
            session.close()
    
    def require_authentication(self):
        """Decorator-like function to require authentication"""
        if not self.is_authenticated():
            st.error("Please log in to access this page.")
            st.stop()
    
    def has_role(self, role_name):
        """Check if current user has specific role"""
        if not self.is_authenticated():
            return False
        return role_name in st.session_state.get('user_roles', [])
    
    def is_admin(self):
        """Check if current user is admin"""
        return self.has_role('Admin')

# Global authentication manager instance
auth_manager = AuthenticationManager()
