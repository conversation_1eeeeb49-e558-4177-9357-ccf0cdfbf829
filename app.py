import streamlit as st
import os
import sys

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import settings
from auth.authentication import auth_manager
from ui.login import show_login_page
from ui.dashboard import show_dashboard, show_service_details, show_service_logs, show_audit_logs
from ui.cron_jobs import show_cron_jobs, show_cron_job_details
from ui.components import show_user_info, show_navigation
from database.init_db import create_database, init_default_data

def initialize_app():
    """Initialize the application"""
    # Set page config
    st.set_page_config(
        page_title=settings.PAGE_TITLE,
        page_icon=settings.PAGE_ICON,
        layout=settings.LAYOUT,
        initial_sidebar_state="expanded"
    )
    
    # Initialize database if it doesn't exist
    try:
        engine = create_database()
        init_default_data(engine)
    except Exception as e:
        st.error(f"Database initialization error: {e}")
        st.stop()
    
    # Initialize session state
    if 'page' not in st.session_state:
        st.session_state.page = 'dashboard'

def show_sidebar():
    """Display sidebar with navigation and user info"""
    # Navigation
    selected_page = show_navigation()
    
    # Update page based on selection
    if selected_page == "Dashboard":
        st.session_state.page = 'dashboard'
    elif selected_page == "System Services":
        st.session_state.page = 'details'
    elif selected_page == "Cron Jobs":
        st.session_state.page = 'cron_jobs'
    elif selected_page == "Audit Logs":
        st.session_state.page = 'audit'
    elif selected_page == "Settings":
        st.session_state.page = 'settings'
    
    # User info
    show_user_info()
    
    # Logout button
    st.sidebar.markdown("---")
    if st.sidebar.button("🚪 Logout", use_container_width=True):
        auth_manager.logout()
        st.rerun()

def show_settings():
    """Display settings page"""
    st.title("⚙️ Settings")
    
    # Only admin can access settings
    if not auth_manager.is_admin():
        st.error("Access denied. Admin privileges required.")
        return
    
    st.subheader("Application Settings")
    
    # Database info
    with st.expander("Database Information"):
        st.write(f"**Database URL:** {settings.DATABASE_URL}")
        st.write(f"**SSH Timeout:** {settings.SSH_TIMEOUT} seconds")
        st.write(f"**Session Timeout:** {settings.SESSION_TIMEOUT} seconds")
        st.write(f"**Max Log Lines:** {settings.MAX_LOG_LINES}")
    
    # Server connection test
    st.subheader("Server Connection Test")
    
    from database.init_db import get_db_session
    from database.models import Server
    from services.service_manager import service_manager
    
    session = get_db_session()
    try:
        servers = session.query(Server).filter_by(is_active=True).all()
        
        for server in servers:
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.write(f"**{server.name}** ({server.hostname}:{server.port})")
            
            with col2:
                if st.button(f"Test", key=f"test_{server.id}"):
                    with st.spinner(f"Testing connection to {server.name}..."):
                        success, message = service_manager.test_server_connection(server.id)
                    
                    if success:
                        st.success(f"✅ {message}")
                    else:
                        st.error(f"❌ {message}")
    
    finally:
        session.close()
    
    # Application info
    st.subheader("Application Information")
    st.write(f"**Version:** {settings.APP_VERSION}")
    st.write(f"**Debug Mode:** {settings.DEBUG}")

def main():
    """Main application function"""
    initialize_app()
    
    # Check authentication
    if not auth_manager.is_authenticated():
        show_login_page()
        return
    
    # Show sidebar
    show_sidebar()
    
    # Show main content based on current page
    page = st.session_state.get('page', 'dashboard')
    
    if page == 'dashboard':
        show_dashboard()
    elif page == 'details':
        show_service_details()
    elif page == 'logs':
        show_service_logs()
    elif page == 'cron_jobs':
        show_cron_jobs()
    elif page == 'cron_details':
        show_cron_job_details()
    elif page == 'audit':
        show_audit_logs()
    elif page == 'settings':
        show_settings()
    else:
        st.error("Page not found")

if __name__ == "__main__":
    main()
