# Service Management Portal

A centralized web-based service management portal built with Streamlit that allows you to manage services across multiple servers from a single interface.

## Features

### 🔐 Authentication & Authorization
- Secure login system with session management
- Role-based access control (RBAC)
- Three default roles: Admin, Operator, Viewer
- Granular permissions per service

### 🖥️ Service Management
- View service status across multiple servers
- Start, stop, and restart services
- Real-time service status monitoring
- Support for systemd services

### 📋 Logging & Monitoring
- Real-time log viewing with configurable line counts
- Support for both log files and journalctl
- Log search functionality
- Auto-refresh capabilities

### 🌐 Multi-server Support
- Manage services on multiple remote servers
- SSH key-based authentication
- Connection testing and validation
- Localhost support for local services

### 📊 Audit & Reporting
- Complete audit trail of all service operations
- User activity tracking
- Filterable audit logs
- Export capabilities

## Quick Start

### Prerequisites
- Docker and Docker Compose
- SSH access to target servers (for remote management)
- systemd-based services on target servers (optional)

### Docker Deployment (Recommended)

1. **Clone or download the project:**
```bash
git clone <repository-url>
cd service_management_portal
```

2. **Deploy with Docker:**
```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

3. **Access the portal:**
Open your browser and navigate to `http://localhost:8501`

### Manual Installation (Alternative)

1. **Install Python dependencies:**
```bash
pip install -r requirements.txt
```

2. **Configure environment:**
```bash
cp .env.example .env
# Edit .env file with your settings
```

3. **Initialize the database:**
```bash
python database/init_db.py
```

4. **Run the application:**
```bash
streamlit run app.py
```

### Default Login
- **Username:** `admin`
- **Password:** `admin123`

⚠️ **Important:** Change the default password after first login!

## Docker Services

The Docker deployment includes several demo services:

- **Service Management Portal**: Main application (port 8501)
- **PostgreSQL Database**: Primary database (port 5432)
- **Demo Nginx**: Sample web server (port 8080)
- **Demo Apache**: Sample web server (port 8081)
- **Demo MySQL**: Sample database (port 3306)
- **Demo Redis**: Sample cache (port 6379)
- **Portainer**: Docker management UI (port 9000)

### Service Management

The portal can manage both:
1. **Docker Containers**: Services running as Docker containers
2. **Remote Services**: Services on remote servers via SSH

## Configuration

### Environment Variables
Create a `.env` file in the project root:

```env
# Database (PostgreSQL for Docker, SQLite for manual)
DATABASE_URL=**************************************/service_portal

# Security
SECRET_KEY=your-secret-key-here
SESSION_TIMEOUT=3600

# SSH Settings
SSH_TIMEOUT=30
SSH_KEY_PATH=~/.ssh/id_rsa

# Application
DEBUG=False
LOG_LEVEL=INFO
MAX_LOG_LINES=1000

# Default Admin (for initial setup)
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin123
DEFAULT_ADMIN_EMAIL=<EMAIL>
```

### SSH Setup
For remote server management, ensure:

1. SSH key-based authentication is configured
2. The user has sudo privileges on target servers
3. SSH keys are accessible from the portal server
4. For Docker deployment, place SSH keys in `ssh_keys/` directory

## Docker Management

### Docker Commands

```bash
# Deploy the entire stack
./scripts/deploy.sh

# View logs
docker-compose logs -f portal
docker-compose logs -f db

# Stop all services
docker-compose down

# Restart services
docker-compose restart

# Update and restart
docker-compose pull && docker-compose up -d

# Backup data
./scripts/backup.sh

# Restore from backup
./scripts/restore.sh backups/service_portal_backup_YYYYMMDD_HHMMSS.tar.gz
```

### Adding SSH Keys for Remote Servers

1. Create SSH keys directory:
```bash
mkdir -p ssh_keys
chmod 700 ssh_keys
```

2. Copy your SSH private key:
```bash
cp ~/.ssh/id_rsa ssh_keys/
chmod 600 ssh_keys/id_rsa
```

3. The key will be available inside the container at `/home/<USER>/.ssh/id_rsa`

## Project Structure

```
service_management_portal/
├── app.py                      # Main Streamlit application
├── Dockerfile                  # Docker container definition
├── docker-compose.yml          # Multi-service Docker setup
├── requirements.txt            # Python dependencies
├── config/
│   ├── settings.py            # Application configuration
│   └── database.py            # Database configuration
├── auth/
│   ├── authentication.py      # Login/logout functionality
│   └── authorization.py       # Role-based access control
├── services/
│   ├── service_manager.py     # Service operations
│   ├── server_connector.py    # SSH/API communication
│   ├── docker_connector.py    # Docker container management
│   └── log_viewer.py         # Real-time log viewing
├── database/
│   ├── models.py             # SQLAlchemy models
│   ├── init_db.py            # Database initialization
│   └── service_portal.db     # SQLite database (manual install)
├── ui/
│   ├── dashboard.py          # Main dashboard interface
│   ├── login.py              # Login page
│   └── components.py         # Reusable UI components
├── utils/
│   ├── helpers.py            # Utility functions
│   └── validators.py         # Input validation
├── docker/                    # Docker configuration files
│   ├── nginx/                # Nginx demo service config
│   ├── apache/               # Apache demo service config
│   └── init-db/              # Database initialization scripts
├── scripts/                   # Deployment and management scripts
│   ├── deploy.sh             # Automated deployment
│   ├── backup.sh             # Backup script
│   └── restore.sh            # Restore script
├── ssh_keys/                  # SSH keys for remote servers
└── logs/                      # Application logs
```

## Database Schema

### Core Tables
- **users**: User accounts and credentials
- **roles**: User roles (Admin, Operator, Viewer)
- **user_roles**: Many-to-many relationship between users and roles
- **servers**: Server configurations and connection details
- **services**: Service definitions and configurations
- **service_permissions**: Granular permissions per role/service
- **audit_logs**: Complete audit trail of all operations

## Usage Guide

### Adding Servers
1. Login as Admin
2. Navigate to Settings
3. Add server configuration with SSH details
4. Test connection

### Adding Services
1. Ensure server is configured
2. Add service with systemd service name
3. Configure log file path (optional)
4. Set permissions for roles

### Managing Services
1. View service status on dashboard
2. Use control buttons to start/stop/restart
3. View logs in real-time
4. Monitor service health

### User Management
1. Admin can create new users
2. Assign roles to users
3. Configure service permissions per role
4. Monitor user activity via audit logs

## Security Considerations

### Authentication
- Passwords are hashed using bcrypt
- Session-based authentication with timeout
- Input validation and sanitization

### Authorization
- Role-based access control
- Granular permissions per service
- Audit logging for all operations

### Network Security
- SSH key-based authentication for servers
- No password storage for server access
- Connection timeout and error handling

### Deployment Security
- Change default credentials
- Use strong secret keys
- Configure proper SSH key permissions
- Regular security updates

## Troubleshooting

### Docker Issues

**Container Won't Start:**
```bash
# Check container logs
docker-compose logs portal
docker-compose logs db

# Check container status
docker-compose ps

# Rebuild containers
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

**Database Connection Error:**
```bash
# Check database container
docker-compose logs db

# Restart database
docker-compose restart db

# Wait for database to be ready
docker-compose exec db pg_isready -U postgres
```

**Permission Issues with SSH Keys:**
```bash
# Fix SSH key permissions
chmod 700 ssh_keys
chmod 600 ssh_keys/*
```

### Common Issues

**SSH Connection Failed:**
- Verify SSH key path and permissions
- Check server hostname and port
- Ensure user has sudo privileges
- For Docker: ensure SSH keys are in `ssh_keys/` directory

**Service Not Found:**
- For systemd: verify service name exists on target server
- For Docker: check container name in docker-compose.yml
- Confirm user permissions

**Permission Denied:**
- Check user role assignments
- Verify service permissions
- Ensure proper SSH key setup

**Docker Container Management Issues:**
- Ensure Docker socket is mounted: `/var/run/docker.sock:/var/run/docker.sock`
- Check if containers have proper labels for portal management
- Verify Docker daemon is running

### Logs and Debugging
```bash
# Application logs
docker-compose logs -f portal

# Database logs
docker-compose logs -f db

# All services logs
docker-compose logs -f

# Enable debug mode in .env
DEBUG=True
```

## Development

### Adding New Features
1. Follow the existing project structure
2. Add appropriate tests
3. Update documentation
4. Follow security best practices

### Database Migrations
- Modify models in `database/models.py`
- Update initialization in `database/init_db.py`
- Test with fresh database

### Custom Authentication
- Extend `auth/authentication.py`
- Implement custom providers
- Update UI components accordingly

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
1. Check the troubleshooting section
2. Review the documentation
3. Check existing issues
4. Create a new issue with detailed information

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

---

**Note:** This is a powerful tool that can control critical services. Always test in a development environment before deploying to production.
