# Linux System Services & Cron Jobs Management Portal

A centralized web-based management portal built with Streamlit that allows you to manage Linux system services and cron jobs across multiple servers from a single interface.

## Features

### 🔐 Authentication & Authorization
- Secure login system with session management
- Role-based access control (RBAC)
- Three default roles: Admin, Operator, Viewer
- Granular permissions per service and cron job

### 🖥️ Linux System Services Management
- View systemd service status across multiple servers
- Start, stop, and restart systemd services
- Real-time service status monitoring
- Support for systemd and init.d services
- Service dependency management

### ⏰ Cron Jobs Management
- View and manage cron jobs across servers
- Create, edit, and delete cron jobs
- Enable/disable cron jobs
- View cron job execution logs
- Schedule validation and syntax checking

### 📋 Logging & Monitoring
- Real-time log viewing with configurable line counts
- Support for systemd journalctl logs
- Cron job execution logs
- Log search functionality
- Auto-refresh capabilities

### 🌐 Multi-server Support
- Manage Linux services and cron jobs on multiple remote servers
- SSH key-based authentication
- Connection testing and validation
- Localhost support for local system management

### 📊 Audit & Reporting
- Complete audit trail of all system service and cron job operations
- User activity tracking
- Filterable audit logs
- Export capabilities

## Quick Start

### Prerequisites
- <PERSON><PERSON> and <PERSON><PERSON> Compose
- SSH access to target Linux servers (for remote management)
- Linux servers with systemd or init.d services
- Cron daemon running on target servers

### Docker Deployment (Recommended)

1. **Clone or download the project:**
```bash
git clone <repository-url>
cd service_management_portal
```

2. **Deploy with Docker:**
```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

3. **Access the portal:**
Open your browser and navigate to `http://localhost:8501`

### Manual Installation (Alternative)

1. **Install Python dependencies:**
```bash
pip install -r requirements.txt
```

2. **Configure environment:**
```bash
cp .env.example .env
# Edit .env file with your settings
```

3. **Initialize the database:**
```bash
python database/init_db.py
```

4. **Run the application:**
```bash
streamlit run app.py
```

### Default Login
- **Username:** `admin`
- **Password:** `admin123`

⚠️ **Important:** Change the default password after first login!

## Docker Services

The Docker deployment includes several demo services:

- **Linux System Services Portal**: Main application (port 8501)
- **PostgreSQL Database**: Primary database (port 5432)
- **Demo Nginx**: Sample web server (port 8080)
- **Demo Apache**: Sample web server (port 8081)
- **Demo MySQL**: Sample database (port 3306)
- **Demo Redis**: Sample cache (port 6379)
- **Portainer**: Docker management UI (port 9000)

### System Management Scope

The portal manages Linux system components:
1. **Linux System Services**: systemd services, init.d services on remote Linux servers via SSH
2. **Cron Jobs**: Scheduled tasks and cron jobs on Linux servers
3. **Docker Containers**: For demo purposes and portal infrastructure

## Configuration

### Environment Variables
Create a `.env` file in the project root:

```env
# Database (PostgreSQL for Docker, SQLite for manual)
DATABASE_URL=**************************************/service_portal

# Security
SECRET_KEY=your-secret-key-here
SESSION_TIMEOUT=3600

# SSH Settings
SSH_TIMEOUT=30
SSH_KEY_PATH=~/.ssh/id_rsa

# Application
DEBUG=False
LOG_LEVEL=INFO
MAX_LOG_LINES=1000

# Default Admin (for initial setup)
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin123
DEFAULT_ADMIN_EMAIL=<EMAIL>
```

### SSH Setup
For remote server management, ensure:

1. SSH key-based authentication is configured
2. The user has sudo privileges on target servers
3. SSH keys are accessible from the portal server
4. For Docker deployment, place SSH keys in `ssh_keys/` directory

## Docker Management

### Docker Commands

```bash
# Deploy the entire stack
./scripts/deploy.sh

# View logs
docker-compose logs -f portal
docker-compose logs -f db

# Stop all services
docker-compose down

# Restart services
docker-compose restart

# Update and restart
docker-compose pull && docker-compose up -d

# Backup data
./scripts/backup.sh

# Restore from backup
./scripts/restore.sh backups/service_portal_backup_YYYYMMDD_HHMMSS.tar.gz
```

### Adding SSH Keys for Remote Servers

1. Create SSH keys directory:
```bash
mkdir -p ssh_keys
chmod 700 ssh_keys
```

2. Copy your SSH private key:
```bash
cp ~/.ssh/id_rsa ssh_keys/
chmod 600 ssh_keys/id_rsa
```

3. The key will be available inside the container at `/home/<USER>/.ssh/id_rsa`

## Project Structure

```
service_management_portal/
├── app.py                      # Main Streamlit application
├── Dockerfile                  # Docker container definition
├── docker-compose.yml          # Multi-service Docker setup
├── requirements.txt            # Python dependencies
├── config/
│   ├── settings.py            # Application configuration
│   └── database.py            # Database configuration
├── auth/
│   ├── authentication.py      # Login/logout functionality
│   └── authorization.py       # Role-based access control
├── services/
│   ├── service_manager.py     # Service operations
│   ├── server_connector.py    # SSH/API communication
│   ├── docker_connector.py    # Docker container management
│   └── log_viewer.py         # Real-time log viewing
├── database/
│   ├── models.py             # SQLAlchemy models
│   ├── init_db.py            # Database initialization
│   └── service_portal.db     # SQLite database (manual install)
├── ui/
│   ├── dashboard.py          # Main dashboard interface
│   ├── login.py              # Login page
│   └── components.py         # Reusable UI components
├── utils/
│   ├── helpers.py            # Utility functions
│   └── validators.py         # Input validation
├── docker/                    # Docker configuration files
│   ├── nginx/                # Nginx demo service config
│   ├── apache/               # Apache demo service config
│   └── init-db/              # Database initialization scripts
├── scripts/                   # Deployment and management scripts
│   ├── deploy.sh             # Automated deployment
│   ├── backup.sh             # Backup script
│   └── restore.sh            # Restore script
├── ssh_keys/                  # SSH keys for remote servers
└── logs/                      # Application logs
```

## Database Schema

### Core Tables
- **users**: User accounts and credentials
- **roles**: User roles (Admin, Operator, Viewer)
- **user_roles**: Many-to-many relationship between users and roles
- **servers**: Linux server configurations and SSH connection details
- **services**: Linux system service definitions and configurations
- **cron_jobs**: Cron job definitions and schedules
- **service_permissions**: Granular permissions per role/service/cron job
- **audit_logs**: Complete audit trail of all system operations

## Usage Guide

### Adding Linux Servers
1. Login as Admin
2. Navigate to Settings
3. Add Linux server configuration with SSH details
4. Test connection to ensure SSH access

### Adding System Services
1. Ensure Linux server is configured
2. Add systemd or init.d service with service name
3. Configure log file path (optional for systemd, uses journalctl)
4. Set permissions for roles

### Managing Cron Jobs
1. Navigate to Cron Jobs section
2. View existing cron jobs across servers
3. Create new cron jobs with schedule syntax
4. Enable/disable cron jobs as needed
5. View cron execution logs

### Managing System Services
1. View systemd service status on dashboard
2. Use control buttons to start/stop/restart services
3. View service logs in real-time (journalctl integration)
4. Monitor service health and dependencies

### User Management
1. Admin can create new users
2. Assign roles to users
3. Configure permissions per system service and cron job
4. Monitor user activity via audit logs

## Security Considerations

### Authentication
- Passwords are hashed using bcrypt
- Session-based authentication with timeout
- Input validation and sanitization

### Authorization
- Role-based access control
- Granular permissions per service
- Audit logging for all operations

### Network Security
- SSH key-based authentication for servers
- No password storage for server access
- Connection timeout and error handling

### Deployment Security
- Change default credentials
- Use strong secret keys
- Configure proper SSH key permissions
- Regular security updates

## Troubleshooting

### Docker Issues

**Container Won't Start:**
```bash
# Check container logs
docker-compose logs portal
docker-compose logs db

# Check container status
docker-compose ps

# Rebuild containers
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

**Database Connection Error:**
```bash
# Check database container
docker-compose logs db

# Restart database
docker-compose restart db

# Wait for database to be ready
docker-compose exec db pg_isready -U postgres
```

**Permission Issues with SSH Keys:**
```bash
# Fix SSH key permissions
chmod 700 ssh_keys
chmod 600 ssh_keys/*
```

### Common Issues

**SSH Connection Failed:**
- Verify SSH key path and permissions
- Check Linux server hostname and port
- Ensure user has sudo privileges on target Linux server
- For Docker: ensure SSH keys are in `ssh_keys/` directory

**System Service Not Found:**
- For systemd: verify service name exists on target Linux server (`systemctl list-units`)
- For init.d: check service exists in `/etc/init.d/`
- Confirm user permissions and sudo access

**Cron Job Issues:**
- Verify cron daemon is running on target server (`systemctl status cron`)
- Check crontab syntax is valid
- Ensure user has permission to modify crontab
- For system-wide cron jobs, ensure sudo access

**Permission Denied:**
- Check user role assignments
- Verify system service and cron job permissions
- Ensure proper SSH key setup and sudo privileges

**Docker Container Management Issues:**
- Ensure Docker socket is mounted: `/var/run/docker.sock:/var/run/docker.sock`
- Check if containers have proper labels for portal management
- Verify Docker daemon is running

### Logs and Debugging
```bash
# Application logs
docker-compose logs -f portal

# Database logs
docker-compose logs -f db

# All services logs
docker-compose logs -f

# Enable debug mode in .env
DEBUG=True
```

## Development

### Adding New Features
1. Follow the existing project structure
2. Add appropriate tests
3. Update documentation
4. Follow security best practices

### Database Migrations
- Modify models in `database/models.py`
- Update initialization in `database/init_db.py`
- Test with fresh database

### Custom Authentication
- Extend `auth/authentication.py`
- Implement custom providers
- Update UI components accordingly

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
1. Check the troubleshooting section
2. Review the documentation
3. Check existing issues
4. Create a new issue with detailed information

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

---

**Note:** This is a powerful tool that can control critical Linux system services and cron jobs. Always test in a development environment before deploying to production. Ensure proper backup procedures are in place before making changes to production systems.
