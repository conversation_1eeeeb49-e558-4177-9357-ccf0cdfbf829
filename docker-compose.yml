services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: service_portal_db
    environment:
      POSTGRES_DB: service_portal
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init-db:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"
    networks:
      - service_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Service Management Portal
  portal:
    build: .
    container_name: service_portal_app
    environment:
      - DATABASE_URL=**************************************/service_portal
      - SECRET_KEY=your-secret-key-change-in-production
      - SESSION_TIMEOUT=3600
      - SSH_TIMEOUT=30
      - DEBUG=false
      - LOG_LEVEL=INFO
      - MAX_LOG_LINES=1000
    volumes:
      - ./ssh_keys:/home/<USER>/.ssh:ro
      - ./logs:/app/logs
      - /var/run/docker.sock:/var/run/docker.sock:ro
    ports:
      - "8501:8501"
    depends_on:
      db:
        condition: service_healthy
    networks:
      - service_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MySQL Database (for demonstration)
  mysql:
    image: mysql:8.0
    container_name: demo_mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: testdb
      MYSQL_USER: testuser
      MYSQL_PASSWORD: testpass
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - service_network
    restart: unless-stopped
    labels:
      - "service.portal.managed=true"
      - "service.portal.name=mysql"
      - "service.portal.description=MySQL Database Server"

volumes:
  postgres_data:
    driver: local
  mysql_data:
    driver: local

networks:
  service_network:
    driver: bridge
