from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, Foreign<PERSON>ey, Text, Table
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import bcrypt

Base = declarative_base()

# Association table for many-to-many relationship between users and roles
user_roles = Table('user_roles', Base.metadata,
    Column('user_id', Integer, ForeignKey('users.id'), primary_key=True),
    Column('role_id', Integer, ForeignKey('roles.id'), primary_key=True)
)

class User(Base):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(<PERSON>olean, default=True)
    
    # Relationships
    roles = relationship("Role", secondary=user_roles, back_populates="users")
    audit_logs = relationship("AuditLog", back_populates="user")
    
    def set_password(self, password):
        """Hash and set password"""
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def check_password(self, password):
        """Check if provided password matches hash"""
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))
    
    def has_permission(self, service_id, permission_type):
        """Check if user has specific permission for a service"""
        for role in self.roles:
            for perm in role.service_permissions:
                if perm.service_id == service_id:
                    return getattr(perm, permission_type, False)
        return False

class Role(Base):
    __tablename__ = 'roles'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False)
    description = Column(Text)
    
    # Relationships
    users = relationship("User", secondary=user_roles, back_populates="roles")
    service_permissions = relationship("ServicePermission", back_populates="role")
    cron_job_permissions = relationship("CronJobPermission", back_populates="role")

class Server(Base):
    __tablename__ = 'servers'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    hostname = Column(String(255), nullable=False)
    port = Column(Integer, default=22)
    username = Column(String(50), nullable=False)
    key_path = Column(String(255))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    services = relationship("Service", back_populates="server")
    cron_jobs = relationship("CronJob", back_populates="server")

class Service(Base):
    __tablename__ = 'services'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    server_id = Column(Integer, ForeignKey('servers.id'), nullable=False)
    service_command = Column(String(255), nullable=False)  # systemctl service name or init.d service
    log_path = Column(String(255))  # Optional, uses journalctl for systemd
    description = Column(Text)
    service_type = Column(String(20), default='systemd')  # 'systemd' or 'initd'
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    server = relationship("Server", back_populates="services")
    permissions = relationship("ServicePermission", back_populates="service")
    audit_logs = relationship("AuditLog", back_populates="service")

class CronJob(Base):
    __tablename__ = 'cron_jobs'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    server_id = Column(Integer, ForeignKey('servers.id'), nullable=False)
    schedule = Column(String(100), nullable=False)  # Cron schedule expression (e.g., "0 2 * * *")
    command = Column(Text, nullable=False)  # Command to execute
    user = Column(String(50), default='root')  # User to run the cron job as
    description = Column(Text)
    is_enabled = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_run = Column(DateTime)  # Last execution time (if available)

    # Relationships
    server = relationship("Server", back_populates="cron_jobs")
    permissions = relationship("CronJobPermission", back_populates="cron_job")
    audit_logs = relationship("AuditLog", back_populates="cron_job")

class ServicePermission(Base):
    __tablename__ = 'service_permissions'
    
    id = Column(Integer, primary_key=True)
    role_id = Column(Integer, ForeignKey('roles.id'), nullable=False)
    service_id = Column(Integer, ForeignKey('services.id'), nullable=False)
    can_view = Column(Boolean, default=True)
    can_start = Column(Boolean, default=False)
    can_stop = Column(Boolean, default=False)
    can_restart = Column(Boolean, default=False)
    
    # Relationships
    role = relationship("Role", back_populates="service_permissions")
    service = relationship("Service", back_populates="permissions")

class CronJobPermission(Base):
    __tablename__ = 'cron_job_permissions'

    id = Column(Integer, primary_key=True)
    role_id = Column(Integer, ForeignKey('roles.id'), nullable=False)
    cron_job_id = Column(Integer, ForeignKey('cron_jobs.id'), nullable=False)
    can_view = Column(Boolean, default=True)
    can_create = Column(Boolean, default=False)
    can_edit = Column(Boolean, default=False)
    can_delete = Column(Boolean, default=False)
    can_enable_disable = Column(Boolean, default=False)

    # Relationships
    role = relationship("Role", back_populates="cron_job_permissions")
    cron_job = relationship("CronJob", back_populates="permissions")

class AuditLog(Base):
    __tablename__ = 'audit_logs'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    service_id = Column(Integer, ForeignKey('services.id'), nullable=True)
    cron_job_id = Column(Integer, ForeignKey('cron_jobs.id'), nullable=True)
    action = Column(String(50), nullable=False)  # start, stop, restart, view_logs, create_cron, edit_cron, delete_cron
    timestamp = Column(DateTime, default=datetime.utcnow)
    result = Column(String(20), nullable=False)  # success, failed
    details = Column(Text)

    # Relationships
    user = relationship("User", back_populates="audit_logs")
    service = relationship("Service", back_populates="audit_logs")
    cron_job = relationship("CronJob", back_populates="audit_logs")
