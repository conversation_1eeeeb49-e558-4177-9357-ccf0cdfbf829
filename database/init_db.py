import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from database.models import Base, User, Role, Server, Service, ServicePermission
from config.settings import settings

def create_database():
    """Create database and tables"""
    database_url = settings.DATABASE_URL

    # For SQLite, ensure database directory exists
    if database_url.startswith('sqlite'):
        db_dir = os.path.dirname(database_url.replace('sqlite:///', ''))
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)

    # For PostgreSQL, add connection pool settings
    if database_url.startswith('postgresql'):
        engine = create_engine(
            database_url,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            pool_recycle=300
        )
    else:
        engine = create_engine(database_url)

    Base.metadata.create_all(engine)
    return engine

def init_default_data(engine):
    """Initialize database with default data"""
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Create default roles
        admin_role = session.query(Role).filter_by(name='Admin').first()
        if not admin_role:
            admin_role = Role(name='Admin', description='Full access to all services')
            session.add(admin_role)
        
        operator_role = session.query(Role).filter_by(name='Operator').first()
        if not operator_role:
            operator_role = Role(name='Operator', description='Can start/stop/restart services')
            session.add(operator_role)
        
        viewer_role = session.query(Role).filter_by(name='Viewer').first()
        if not viewer_role:
            viewer_role = Role(name='Viewer', description='Can only view service status and logs')
            session.add(viewer_role)
        
        session.commit()
        
        # Create default admin user
        admin_user = session.query(User).filter_by(username=settings.DEFAULT_ADMIN_USERNAME).first()
        if not admin_user:
            admin_user = User(
                username=settings.DEFAULT_ADMIN_USERNAME,
                email=settings.DEFAULT_ADMIN_EMAIL
            )
            admin_user.set_password(settings.DEFAULT_ADMIN_PASSWORD)
            admin_user.roles.append(admin_role)
            session.add(admin_user)
        
        # Create sample servers
        localhost_server = session.query(Server).filter_by(name='localhost').first()
        if not localhost_server:
            localhost_server = Server(
                name='localhost',
                hostname='localhost',
                port=22,
                username='root',
                key_path=settings.SSH_KEY_PATH
            )
            session.add(localhost_server)

        # Create Docker server for container management
        docker_server = session.query(Server).filter_by(name='docker').first()
        if not docker_server:
            docker_server = Server(
                name='docker',
                hostname='docker',
                port=0,  # Not used for Docker
                username='docker',
                key_path=''  # Not used for Docker
            )
            session.add(docker_server)
        
        session.commit()
        
        # Create sample Linux system services for localhost
        localhost_services = [
            {
                'name': 'nginx',
                'service_command': 'nginx',
                'log_path': '/var/log/nginx/access.log',
                'description': 'Nginx web server (systemd service)',
                'service_type': 'systemd',
                'server_id': localhost_server.id
            },
            {
                'name': 'apache2',
                'service_command': 'apache2',
                'log_path': '/var/log/apache2/access.log',
                'description': 'Apache web server (systemd service)',
                'service_type': 'systemd',
                'server_id': localhost_server.id
            },
            {
                'name': 'mysql',
                'service_command': 'mysql',
                'log_path': '',  # Uses journalctl for systemd
                'description': 'MySQL database server (systemd service)',
                'service_type': 'systemd',
                'server_id': localhost_server.id
            },
            {
                'name': 'ssh',
                'service_command': 'ssh',
                'log_path': '',  # Uses journalctl for systemd
                'description': 'SSH daemon (systemd service)',
                'service_type': 'systemd',
                'server_id': localhost_server.id
            },
            {
                'name': 'cron',
                'service_command': 'cron',
                'log_path': '/var/log/cron.log',
                'description': 'Cron daemon (systemd service)',
                'service_type': 'systemd',
                'server_id': localhost_server.id
            }
        ]

        # Create Docker container services
        docker_services = [
            {
                'name': 'demo_nginx',
                'service_command': 'demo_nginx',
                'log_path': '',
                'description': 'Demo Nginx web server (Docker)',
                'server_id': docker_server.id
            },
            {
                'name': 'demo_apache',
                'service_command': 'demo_apache',
                'log_path': '',
                'description': 'Demo Apache web server (Docker)',
                'server_id': docker_server.id
            },
            {
                'name': 'demo_mysql',
                'service_command': 'demo_mysql',
                'log_path': '',
                'description': 'Demo MySQL database (Docker)',
                'server_id': docker_server.id
            },
            {
                'name': 'demo_redis',
                'service_command': 'demo_redis',
                'log_path': '',
                'description': 'Demo Redis cache (Docker)',
                'server_id': docker_server.id
            }
        ]

        # Combine all services
        all_services = localhost_services + docker_services
        
        for service_data in all_services:
            existing_service = session.query(Service).filter_by(
                name=service_data['name'],
                server_id=service_data['server_id']
            ).first()
            if not existing_service:
                service = Service(
                    name=service_data['name'],
                    server_id=service_data['server_id'],
                    service_command=service_data['service_command'],
                    log_path=service_data['log_path'],
                    description=service_data['description'],
                    service_type=service_data.get('service_type', 'systemd')
                )
                session.add(service)
        
        session.commit()

        # Create sample cron jobs
        from database.models import CronJob

        sample_cron_jobs = [
            {
                'name': 'Daily System Backup',
                'server_id': localhost_server.id,
                'schedule': '0 2 * * *',
                'command': '/usr/local/bin/backup-system.sh',
                'user': 'root',
                'description': 'Daily backup of system files at 2 AM'
            },
            {
                'name': 'Log Rotation',
                'server_id': localhost_server.id,
                'schedule': '0 0 * * 0',
                'command': '/usr/sbin/logrotate /etc/logrotate.conf',
                'user': 'root',
                'description': 'Weekly log rotation on Sunday at midnight'
            },
            {
                'name': 'Disk Cleanup',
                'server_id': localhost_server.id,
                'schedule': '30 3 1 * *',
                'command': '/usr/bin/find /tmp -type f -atime +7 -delete',
                'user': 'root',
                'description': 'Monthly cleanup of temporary files'
            },
            {
                'name': 'System Update Check',
                'server_id': localhost_server.id,
                'schedule': '0 6 * * 1',
                'command': '/usr/bin/apt update && /usr/bin/apt list --upgradable',
                'user': 'root',
                'description': 'Weekly check for system updates on Monday at 6 AM'
            }
        ]

        for cron_data in sample_cron_jobs:
            existing_cron = session.query(CronJob).filter_by(
                name=cron_data['name'],
                server_id=cron_data['server_id']
            ).first()
            if not existing_cron:
                cron_job = CronJob(
                    name=cron_data['name'],
                    server_id=cron_data['server_id'],
                    schedule=cron_data['schedule'],
                    command=cron_data['command'],
                    user=cron_data['user'],
                    description=cron_data['description']
                )
                session.add(cron_job)

        session.commit()

        print("Database initialized successfully!")
        print(f"Default admin user: {settings.DEFAULT_ADMIN_USERNAME}")
        print(f"Default admin password: {settings.DEFAULT_ADMIN_PASSWORD}")
        print("Sample Linux system services and cron jobs created.")
        
    except Exception as e:
        session.rollback()
        print(f"Error initializing database: {e}")
        raise
    finally:
        session.close()

def get_db_session():
    """Get database session"""
    engine = create_engine(settings.DATABASE_URL)
    Session = sessionmaker(bind=engine)
    return Session()

if __name__ == "__main__":
    engine = create_database()
    init_default_data(engine)
