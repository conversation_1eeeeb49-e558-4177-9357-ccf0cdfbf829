from datetime import datetime
from database.init_db import get_db_session
from database.models import Cron<PERSON>ob, Server, AuditLog
from services.server_connector import ServerConnector
from auth.authentication import auth_manager

class CronJobManager:
    def __init__(self):
        pass
    
    def get_cron_job_status(self, cron_job_id):
        """Get current status of a cron job"""
        session = get_db_session()
        try:
            cron_job = session.query(CronJob).filter_by(id=cron_job_id).first()
            if not cron_job:
                return False, "Cron job not found", None

            server = cron_job.server

            # Use SSH for remote cron job management
            connector = ServerConnector(
                hostname=server.hostname,
                port=server.port,
                username=server.username,
                key_path=server.key_path
            )

            # Check if cron job exists and is enabled
            success, crontab_content = connector.list_cron_jobs(cron_job.user)
            connector.disconnect()
            
            if not success:
                return False, f"Failed to read crontab: {crontab_content}", cron_job
            
            # Check if the job exists in crontab
            job_line = f"{cron_job.schedule} {cron_job.command}"
            commented_job_line = f"#{cron_job.schedule} {cron_job.command}"
            
            if job_line in crontab_content:
                status = "enabled"
            elif commented_job_line in crontab_content:
                status = "disabled"
            else:
                status = "not_found"
            
            return True, status, cron_job

        except Exception as e:
            return False, f"Error getting cron job status: {str(e)}", None
        finally:
            session.close()
    
    def create_cron_job(self, name, server_id, schedule, command, user='root', description=''):
        """Create a new cron job"""
        session = get_db_session()
        try:
            server = session.query(Server).filter_by(id=server_id).first()
            if not server:
                return False, "Server not found"

            # Validate cron schedule (basic validation)
            if not self._validate_cron_schedule(schedule):
                return False, "Invalid cron schedule format"

            # Create cron job in database
            cron_job = CronJob(
                name=name,
                server_id=server_id,
                schedule=schedule,
                command=command,
                user=user,
                description=description
            )
            session.add(cron_job)
            session.commit()

            # Add cron job to server
            connector = ServerConnector(
                hostname=server.hostname,
                port=server.port,
                username=server.username,
                key_path=server.key_path
            )

            success, message = connector.add_cron_job(schedule, command, user)
            connector.disconnect()

            if success:
                # Log the action
                self._log_action(session, cron_job.id, 'create_cron', 'success', f"Created cron job: {name}")
                return True, f"Cron job '{name}' created successfully"
            else:
                # Remove from database if server operation failed
                session.delete(cron_job)
                session.commit()
                return False, f"Failed to create cron job on server: {message}"

        except Exception as e:
            session.rollback()
            return False, f"Error creating cron job: {str(e)}"
        finally:
            session.close()
    
    def enable_cron_job(self, cron_job_id):
        """Enable a cron job"""
        return self._perform_cron_action(cron_job_id, 'enable')
    
    def disable_cron_job(self, cron_job_id):
        """Disable a cron job"""
        return self._perform_cron_action(cron_job_id, 'disable')
    
    def delete_cron_job(self, cron_job_id):
        """Delete a cron job"""
        session = get_db_session()
        try:
            cron_job = session.query(CronJob).filter_by(id=cron_job_id).first()
            if not cron_job:
                return False, "Cron job not found"

            server = cron_job.server

            # Remove from server first
            connector = ServerConnector(
                hostname=server.hostname,
                port=server.port,
                username=server.username,
                key_path=server.key_path
            )

            success, message = connector.remove_cron_job(cron_job.schedule, cron_job.command, cron_job.user)
            connector.disconnect()

            if success:
                # Log the action before deleting
                self._log_action(session, cron_job_id, 'delete_cron', 'success', f"Deleted cron job: {cron_job.name}")
                
                # Remove from database
                session.delete(cron_job)
                session.commit()
                return True, f"Cron job '{cron_job.name}' deleted successfully"
            else:
                return False, f"Failed to delete cron job from server: {message}"

        except Exception as e:
            session.rollback()
            return False, f"Error deleting cron job: {str(e)}"
        finally:
            session.close()
    
    def _perform_cron_action(self, cron_job_id, action):
        """Perform cron job action (enable/disable)"""
        session = get_db_session()
        try:
            cron_job = session.query(CronJob).filter_by(id=cron_job_id).first()
            if not cron_job:
                return False, "Cron job not found"

            server = cron_job.server

            connector = ServerConnector(
                hostname=server.hostname,
                port=server.port,
                username=server.username,
                key_path=server.key_path
            )

            # Perform the action
            if action == 'enable':
                success, message = connector.enable_cron_job(cron_job.schedule, cron_job.command, cron_job.user)
                if success:
                    cron_job.is_enabled = True
            elif action == 'disable':
                success, message = connector.disable_cron_job(cron_job.schedule, cron_job.command, cron_job.user)
                if success:
                    cron_job.is_enabled = False
            else:
                return False, f"Unknown action: {action}"

            connector.disconnect()

            if success:
                session.commit()
                # Log the action
                self._log_action(session, cron_job_id, f'{action}_cron', 'success', message)
                return True, message
            else:
                return False, message

        except Exception as e:
            session.rollback()
            return False, f"Error performing {action}: {str(e)}"
        finally:
            session.close()
    
    def get_all_cron_jobs_status(self):
        """Get status of all accessible cron jobs"""
        from auth.authorization import auth_manager_auth
        
        # This would need to be implemented in authorization module
        # For now, get all cron jobs (admin access)
        session = get_db_session()
        try:
            cron_jobs = session.query(CronJob).filter_by(is_enabled=True).all()
            cron_jobs_status = []
            
            for cron_job in cron_jobs:
                success, status, _ = self.get_cron_job_status(cron_job.id)
                cron_jobs_status.append({
                    'id': cron_job.id,
                    'name': cron_job.name,
                    'server': cron_job.server.name,
                    'schedule': cron_job.schedule,
                    'command': cron_job.command,
                    'user': cron_job.user,
                    'description': cron_job.description,
                    'status': status if success else 'unknown',
                    'permissions': {
                        'can_view': True,
                        'can_edit': True,
                        'can_delete': True,
                        'can_enable_disable': True
                    }  # TODO: Implement proper permissions
                })
            
            return cron_jobs_status
        finally:
            session.close()
    
    def _validate_cron_schedule(self, schedule):
        """Basic validation of cron schedule format"""
        parts = schedule.split()
        if len(parts) != 5:
            return False
        
        # Basic validation - could be more comprehensive
        for part in parts:
            if not (part.isdigit() or part == '*' or '/' in part or '-' in part or ',' in part):
                return False
        
        return True
    
    def _log_action(self, session, cron_job_id, action, result, details=None):
        """Log cron job action to audit log"""
        try:
            user = auth_manager.get_current_user()
            if user:
                audit_log = AuditLog(
                    user_id=user.id,
                    cron_job_id=cron_job_id,
                    action=action,
                    result=result,
                    details=details
                )
                session.add(audit_log)
                session.commit()
        except Exception as e:
            print(f"Error logging action: {e}")

# Global cron job manager instance
cron_job_manager = CronJobManager()
