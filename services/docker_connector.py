import docker
import json
from datetime import datetime
from config.settings import settings

class DockerConnector:
    def __init__(self):
        try:
            self.client = docker.from_env()
        except Exception as e:
            print(f"Failed to connect to Docker: {e}")
            self.client = None
    
    def is_available(self):
        """Check if Docker is available"""
        return self.client is not None
    
    def get_container_status(self, container_name):
        """Get Docker container status"""
        try:
            if not self.client:
                return False, "Docker not available"
            
            container = self.client.containers.get(container_name)
            return True, container.status
        
        except docker.errors.NotFound:
            return False, "Container not found"
        except Exception as e:
            return False, f"Error getting status: {str(e)}"
    
    def start_container(self, container_name):
        """Start Docker container"""
        try:
            if not self.client:
                return False, "Docker not available"
            
            container = self.client.containers.get(container_name)
            container.start()
            return True, f"Container {container_name} started successfully"
        
        except docker.errors.NotFound:
            return False, "Container not found"
        except docker.errors.APIError as e:
            return False, f"Docker API error: {str(e)}"
        except Exception as e:
            return False, f"Error starting container: {str(e)}"
    
    def stop_container(self, container_name):
        """Stop Docker container"""
        try:
            if not self.client:
                return False, "Docker not available"
            
            container = self.client.containers.get(container_name)
            container.stop()
            return True, f"Container {container_name} stopped successfully"
        
        except docker.errors.NotFound:
            return False, "Container not found"
        except docker.errors.APIError as e:
            return False, f"Docker API error: {str(e)}"
        except Exception as e:
            return False, f"Error stopping container: {str(e)}"
    
    def restart_container(self, container_name):
        """Restart Docker container"""
        try:
            if not self.client:
                return False, "Docker not available"
            
            container = self.client.containers.get(container_name)
            container.restart()
            return True, f"Container {container_name} restarted successfully"
        
        except docker.errors.NotFound:
            return False, "Container not found"
        except docker.errors.APIError as e:
            return False, f"Docker API error: {str(e)}"
        except Exception as e:
            return False, f"Error restarting container: {str(e)}"
    
    def get_container_logs(self, container_name, lines=100):
        """Get Docker container logs"""
        try:
            if not self.client:
                return False, "Docker not available"
            
            container = self.client.containers.get(container_name)
            logs = container.logs(tail=lines, timestamps=True).decode('utf-8')
            return True, logs
        
        except docker.errors.NotFound:
            return False, "Container not found"
        except Exception as e:
            return False, f"Error getting logs: {str(e)}"
    
    def list_managed_containers(self):
        """List containers managed by the portal"""
        try:
            if not self.client:
                return []
            
            containers = self.client.containers.list(all=True)
            managed_containers = []
            
            for container in containers:
                labels = container.labels or {}
                if labels.get('service.portal.managed') == 'true':
                    managed_containers.append({
                        'id': container.id[:12],
                        'name': container.name,
                        'status': container.status,
                        'image': container.image.tags[0] if container.image.tags else 'unknown',
                        'service_name': labels.get('service.portal.name', container.name),
                        'description': labels.get('service.portal.description', ''),
                        'created': container.attrs['Created']
                    })
            
            return managed_containers
        
        except Exception as e:
            print(f"Error listing containers: {e}")
            return []
    
    def get_container_stats(self, container_name):
        """Get container resource usage statistics"""
        try:
            if not self.client:
                return False, "Docker not available"
            
            container = self.client.containers.get(container_name)
            stats = container.stats(stream=False)
            
            # Calculate CPU percentage
            cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - \
                       stats['precpu_stats']['cpu_usage']['total_usage']
            system_delta = stats['cpu_stats']['system_cpu_usage'] - \
                          stats['precpu_stats']['system_cpu_usage']
            
            cpu_percent = 0.0
            if system_delta > 0:
                cpu_percent = (cpu_delta / system_delta) * 100.0
            
            # Calculate memory usage
            memory_usage = stats['memory_stats']['usage']
            memory_limit = stats['memory_stats']['limit']
            memory_percent = (memory_usage / memory_limit) * 100.0
            
            return True, {
                'cpu_percent': round(cpu_percent, 2),
                'memory_usage': self._format_bytes(memory_usage),
                'memory_limit': self._format_bytes(memory_limit),
                'memory_percent': round(memory_percent, 2),
                'network_rx': self._format_bytes(stats['networks']['eth0']['rx_bytes']),
                'network_tx': self._format_bytes(stats['networks']['eth0']['tx_bytes'])
            }
        
        except docker.errors.NotFound:
            return False, "Container not found"
        except Exception as e:
            return False, f"Error getting stats: {str(e)}"
    
    def _format_bytes(self, bytes_value):
        """Format bytes to human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} TB"
    
    def test_connection(self):
        """Test Docker connection"""
        try:
            if not self.client:
                return False, "Docker client not initialized"
            
            # Try to ping Docker daemon
            self.client.ping()
            return True, "Docker connection successful"
        
        except Exception as e:
            return False, f"Docker connection failed: {str(e)}"

# Global Docker connector instance
docker_connector = DockerConnector()
