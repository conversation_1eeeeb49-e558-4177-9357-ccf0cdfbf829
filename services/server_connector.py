import paramiko
import socket
import subprocess
import os
from config.settings import settings

class ServerConnector:
    def __init__(self, hostname, port=22, username=None, key_path=None):
        self.hostname = hostname
        self.port = port
        self.username = username
        self.key_path = key_path
        self.ssh_client = None
    
    def connect(self):
        """Establish SSH connection to server"""
        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            if self.hostname == 'localhost' or self.hostname == '127.0.0.1':
                # For localhost, we'll use local commands instead of SSH
                return True, "Connected to localhost"
            
            # Use SSH key authentication
            if self.key_path and os.path.exists(os.path.expanduser(self.key_path)):
                self.ssh_client.connect(
                    hostname=self.hostname,
                    port=self.port,
                    username=self.username,
                    key_filename=os.path.expanduser(self.key_path),
                    timeout=settings.SSH_TIMEOUT
                )
            else:
                return False, f"SSH key not found: {self.key_path}"
            
            return True, "Connected successfully"
        
        except paramiko.AuthenticationException:
            return False, "Authentication failed"
        except paramiko.SSHException as e:
            return False, f"SSH connection error: {str(e)}"
        except socket.timeout:
            return False, "Connection timeout"
        except Exception as e:
            return False, f"Connection error: {str(e)}"
    
    def disconnect(self):
        """Close SSH connection"""
        if self.ssh_client:
            self.ssh_client.close()
            self.ssh_client = None
    
    def execute_command(self, command):
        """Execute command on remote server"""
        try:
            if self.hostname == 'localhost' or self.hostname == '127.0.0.1':
                # Execute locally
                result = subprocess.run(
                    command, 
                    shell=True, 
                    capture_output=True, 
                    text=True, 
                    timeout=30
                )
                return True, result.stdout, result.stderr
            
            if not self.ssh_client:
                success, message = self.connect()
                if not success:
                    return False, "", message
            
            stdin, stdout, stderr = self.ssh_client.exec_command(command, timeout=30)
            stdout_data = stdout.read().decode('utf-8')
            stderr_data = stderr.read().decode('utf-8')
            exit_status = stdout.channel.recv_exit_status()
            
            return exit_status == 0, stdout_data, stderr_data
        
        except subprocess.TimeoutExpired:
            return False, "", "Command timeout"
        except Exception as e:
            return False, "", f"Command execution error: {str(e)}"
    
    def get_service_status(self, service_name):
        """Get systemd service status"""
        command = f"systemctl is-active {service_name}"
        success, stdout, stderr = self.execute_command(command)
        
        if success:
            status = stdout.strip()
            return True, status
        else:
            # Try alternative status check
            command = f"systemctl status {service_name} --no-pager -l"
            success, stdout, stderr = self.execute_command(command)
            if "active (running)" in stdout:
                return True, "active"
            elif "inactive" in stdout or "dead" in stdout:
                return True, "inactive"
            else:
                return False, "unknown"
    
    def start_service(self, service_name):
        """Start systemd service"""
        command = f"sudo systemctl start {service_name}"
        success, stdout, stderr = self.execute_command(command)
        return success, stdout if success else stderr
    
    def stop_service(self, service_name):
        """Stop systemd service"""
        command = f"sudo systemctl stop {service_name}"
        success, stdout, stderr = self.execute_command(command)
        return success, stdout if success else stderr
    
    def restart_service(self, service_name):
        """Restart systemd service"""
        command = f"sudo systemctl restart {service_name}"
        success, stdout, stderr = self.execute_command(command)
        return success, stdout if success else stderr

    # Init.d service management methods
    def get_initd_service_status(self, service_name):
        """Get init.d service status"""
        command = f"sudo service {service_name} status"
        success, stdout, stderr = self.execute_command(command)

        if success:
            # Parse output to determine status
            output = stdout.lower()
            if 'running' in output or 'active' in output:
                return True, 'active'
            elif 'stopped' in output or 'inactive' in output:
                return True, 'inactive'
            else:
                return True, 'unknown'
        else:
            return False, stderr

    def start_initd_service(self, service_name):
        """Start init.d service"""
        command = f"sudo service {service_name} start"
        success, stdout, stderr = self.execute_command(command)
        return success, stdout if success else stderr

    def stop_initd_service(self, service_name):
        """Stop init.d service"""
        command = f"sudo service {service_name} stop"
        success, stdout, stderr = self.execute_command(command)
        return success, stdout if success else stderr

    def restart_initd_service(self, service_name):
        """Restart init.d service"""
        command = f"sudo service {service_name} restart"
        success, stdout, stderr = self.execute_command(command)
        return success, stdout if success else stderr

    # Cron job management methods
    def list_cron_jobs(self, user='root'):
        """List cron jobs for a specific user"""
        if user == 'root':
            command = "sudo crontab -l"
        else:
            command = f"sudo crontab -u {user} -l"

        success, stdout, stderr = self.execute_command(command)
        if success:
            return True, stdout
        elif "no crontab for" in stderr.lower():
            return True, ""  # No cron jobs is not an error
        else:
            return False, stderr

    def add_cron_job(self, schedule, command, user='root'):
        """Add a new cron job"""
        # First get existing crontab
        success, current_crontab = self.list_cron_jobs(user)
        if not success:
            return False, f"Failed to read current crontab: {current_crontab}"

        # Add new job
        new_job = f"{schedule} {command}"
        if current_crontab.strip():
            updated_crontab = current_crontab.strip() + "\n" + new_job
        else:
            updated_crontab = new_job

        # Write updated crontab
        return self._write_crontab(updated_crontab, user)

    def remove_cron_job(self, schedule, command, user='root'):
        """Remove a specific cron job"""
        # Get existing crontab
        success, current_crontab = self.list_cron_jobs(user)
        if not success:
            return False, f"Failed to read current crontab: {current_crontab}"

        # Remove the specific job
        job_to_remove = f"{schedule} {command}"
        lines = current_crontab.strip().split('\n')
        updated_lines = [line for line in lines if line.strip() != job_to_remove.strip()]

        updated_crontab = '\n'.join(updated_lines)
        return self._write_crontab(updated_crontab, user)

    def enable_cron_job(self, schedule, command, user='root'):
        """Enable a cron job (remove # comment if present)"""
        success, current_crontab = self.list_cron_jobs(user)
        if not success:
            return False, f"Failed to read current crontab: {current_crontab}"

        job_pattern = f"{schedule} {command}"
        commented_pattern = f"#{schedule} {command}"

        lines = current_crontab.strip().split('\n')
        updated_lines = []
        job_found = False

        for line in lines:
            if line.strip() == commented_pattern.strip():
                updated_lines.append(job_pattern)
                job_found = True
            else:
                updated_lines.append(line)

        if not job_found:
            return False, "Cron job not found or already enabled"

        updated_crontab = '\n'.join(updated_lines)
        return self._write_crontab(updated_crontab, user)

    def disable_cron_job(self, schedule, command, user='root'):
        """Disable a cron job (add # comment)"""
        success, current_crontab = self.list_cron_jobs(user)
        if not success:
            return False, f"Failed to read current crontab: {current_crontab}"

        job_pattern = f"{schedule} {command}"
        commented_pattern = f"#{schedule} {command}"

        lines = current_crontab.strip().split('\n')
        updated_lines = []
        job_found = False

        for line in lines:
            if line.strip() == job_pattern.strip():
                updated_lines.append(commented_pattern)
                job_found = True
            else:
                updated_lines.append(line)

        if not job_found:
            return False, "Cron job not found or already disabled"

        updated_crontab = '\n'.join(updated_lines)
        return self._write_crontab(updated_crontab, user)

    def _write_crontab(self, crontab_content, user='root'):
        """Write crontab content for a specific user"""
        import tempfile
        import os

        try:
            # Create temporary file with crontab content
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.cron') as temp_file:
                temp_file.write(crontab_content)
                temp_file_path = temp_file.name

            # Upload temp file to server and install crontab
            if self.hostname == 'localhost' or self.hostname == '127.0.0.1':
                # For localhost, use local commands
                if user == 'root':
                    command = f"sudo crontab {temp_file_path}"
                else:
                    command = f"sudo crontab -u {user} {temp_file_path}"

                success, stdout, stderr = self.execute_command(command)
                os.unlink(temp_file_path)  # Clean up temp file
                return success, stdout if success else stderr
            else:
                # For remote servers, we need to transfer the file first
                # This is a simplified approach - in production you might want to use SCP
                escaped_content = crontab_content.replace("'", "'\"'\"'")
                if user == 'root':
                    command = f"echo '{escaped_content}' | sudo crontab -"
                else:
                    command = f"echo '{escaped_content}' | sudo crontab -u {user} -"

                success, stdout, stderr = self.execute_command(command)
                os.unlink(temp_file_path)  # Clean up temp file
                return success, stdout if success else stderr

        except Exception as e:
            return False, f"Error writing crontab: {str(e)}"

    def get_cron_logs(self, lines=100):
        """Get cron execution logs"""
        # Try different log locations depending on the system
        log_commands = [
            f"sudo tail -n {lines} /var/log/cron",
            f"sudo tail -n {lines} /var/log/syslog | grep CRON",
            f"sudo journalctl -u cron -n {lines} --no-pager"
        ]

        for command in log_commands:
            success, stdout, stderr = self.execute_command(command)
            if success and stdout.strip():
                return True, stdout

        return False, "No cron logs found or accessible"
    
    def get_service_logs(self, service_name, lines=100):
        """Get service logs using journalctl"""
        command = f"sudo journalctl -u {service_name} -n {lines} --no-pager"
        success, stdout, stderr = self.execute_command(command)
        return success, stdout if success else stderr
    
    def get_log_file_content(self, log_path, lines=100):
        """Get content from log file"""
        if not log_path:
            return False, "No log path specified"
        
        command = f"sudo tail -n {lines} {log_path}"
        success, stdout, stderr = self.execute_command(command)
        return success, stdout if success else stderr
    
    def test_connection(self):
        """Test server connection"""
        success, message = self.connect()
        if success:
            # Test with a simple command
            cmd_success, stdout, stderr = self.execute_command("echo 'test'")
            if cmd_success and "test" in stdout:
                self.disconnect()
                return True, "Connection test successful"
            else:
                self.disconnect()
                return False, "Command execution failed"
        return success, message
