import paramiko
import socket
import subprocess
import os
from config.settings import settings

class ServerConnector:
    def __init__(self, hostname, port=22, username=None, key_path=None):
        self.hostname = hostname
        self.port = port
        self.username = username
        self.key_path = key_path
        self.ssh_client = None
    
    def connect(self):
        """Establish SSH connection to server"""
        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            if self.hostname == 'localhost' or self.hostname == '127.0.0.1':
                # For localhost, we'll use local commands instead of SSH
                return True, "Connected to localhost"
            
            # Use SSH key authentication
            if self.key_path and os.path.exists(os.path.expanduser(self.key_path)):
                self.ssh_client.connect(
                    hostname=self.hostname,
                    port=self.port,
                    username=self.username,
                    key_filename=os.path.expanduser(self.key_path),
                    timeout=settings.SSH_TIMEOUT
                )
            else:
                return False, f"SSH key not found: {self.key_path}"
            
            return True, "Connected successfully"
        
        except paramiko.AuthenticationException:
            return False, "Authentication failed"
        except paramiko.SSHException as e:
            return False, f"SSH connection error: {str(e)}"
        except socket.timeout:
            return False, "Connection timeout"
        except Exception as e:
            return False, f"Connection error: {str(e)}"
    
    def disconnect(self):
        """Close SSH connection"""
        if self.ssh_client:
            self.ssh_client.close()
            self.ssh_client = None
    
    def execute_command(self, command):
        """Execute command on remote server"""
        try:
            if self.hostname == 'localhost' or self.hostname == '127.0.0.1':
                # Execute locally
                result = subprocess.run(
                    command, 
                    shell=True, 
                    capture_output=True, 
                    text=True, 
                    timeout=30
                )
                return True, result.stdout, result.stderr
            
            if not self.ssh_client:
                success, message = self.connect()
                if not success:
                    return False, "", message
            
            stdin, stdout, stderr = self.ssh_client.exec_command(command, timeout=30)
            stdout_data = stdout.read().decode('utf-8')
            stderr_data = stderr.read().decode('utf-8')
            exit_status = stdout.channel.recv_exit_status()
            
            return exit_status == 0, stdout_data, stderr_data
        
        except subprocess.TimeoutExpired:
            return False, "", "Command timeout"
        except Exception as e:
            return False, "", f"Command execution error: {str(e)}"
    
    def get_service_status(self, service_name):
        """Get systemd service status"""
        command = f"systemctl is-active {service_name}"
        success, stdout, stderr = self.execute_command(command)
        
        if success:
            status = stdout.strip()
            return True, status
        else:
            # Try alternative status check
            command = f"systemctl status {service_name} --no-pager -l"
            success, stdout, stderr = self.execute_command(command)
            if "active (running)" in stdout:
                return True, "active"
            elif "inactive" in stdout or "dead" in stdout:
                return True, "inactive"
            else:
                return False, "unknown"
    
    def start_service(self, service_name):
        """Start systemd service"""
        command = f"sudo systemctl start {service_name}"
        success, stdout, stderr = self.execute_command(command)
        return success, stdout if success else stderr
    
    def stop_service(self, service_name):
        """Stop systemd service"""
        command = f"sudo systemctl stop {service_name}"
        success, stdout, stderr = self.execute_command(command)
        return success, stdout if success else stderr
    
    def restart_service(self, service_name):
        """Restart systemd service"""
        command = f"sudo systemctl restart {service_name}"
        success, stdout, stderr = self.execute_command(command)
        return success, stdout if success else stderr
    
    def get_service_logs(self, service_name, lines=100):
        """Get service logs using journalctl"""
        command = f"sudo journalctl -u {service_name} -n {lines} --no-pager"
        success, stdout, stderr = self.execute_command(command)
        return success, stdout if success else stderr
    
    def get_log_file_content(self, log_path, lines=100):
        """Get content from log file"""
        if not log_path:
            return False, "No log path specified"
        
        command = f"sudo tail -n {lines} {log_path}"
        success, stdout, stderr = self.execute_command(command)
        return success, stdout if success else stderr
    
    def test_connection(self):
        """Test server connection"""
        success, message = self.connect()
        if success:
            # Test with a simple command
            cmd_success, stdout, stderr = self.execute_command("echo 'test'")
            if cmd_success and "test" in stdout:
                self.disconnect()
                return True, "Connection test successful"
            else:
                self.disconnect()
                return False, "Command execution failed"
        return success, message
