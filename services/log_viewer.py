import streamlit as st
import time
from services.service_manager import service_manager
from config.settings import settings

class LogViewer:
    def __init__(self):
        self.max_lines = settings.MAX_LOG_LINES
    
    def display_logs(self, service_id, auto_refresh=False, refresh_interval=5):
        """Display service logs with optional auto-refresh"""
        
        # Create columns for controls
        col1, col2, col3, col4 = st.columns([2, 1, 1, 1])
        
        with col1:
            lines_to_show = st.selectbox(
                "Lines to show:",
                [50, 100, 200, 500, 1000],
                index=1,
                key=f"lines_{service_id}"
            )
        
        with col2:
            use_log_file = st.checkbox(
                "Use log file",
                value=True,
                key=f"use_log_file_{service_id}",
                help="Use service log file if available, otherwise use journalctl"
            )
        
        with col3:
            auto_refresh = st.checkbox(
                "Auto-refresh",
                value=auto_refresh,
                key=f"auto_refresh_{service_id}"
            )
        
        with col4:
            if st.button("Refresh Logs", key=f"refresh_{service_id}"):
                st.rerun()
        
        # Display logs
        log_container = st.container()
        
        with log_container:
            with st.spinner("Loading logs..."):
                success, logs = service_manager.get_service_logs(
                    service_id, 
                    lines=lines_to_show,
                    use_log_file=use_log_file
                )
            
            if success:
                if logs.strip():
                    # Display logs in a code block with syntax highlighting
                    st.code(logs, language="bash")
                else:
                    st.info("No logs available")
            else:
                st.error(f"Failed to retrieve logs: {logs}")
        
        # Auto-refresh functionality
        if auto_refresh:
            time.sleep(refresh_interval)
            st.rerun()
    
    def display_logs_sidebar(self, service_id):
        """Display logs in sidebar for compact view"""
        st.sidebar.subheader("Service Logs")
        
        lines_to_show = st.sidebar.selectbox(
            "Lines:",
            [20, 50, 100],
            index=1,
            key=f"sidebar_lines_{service_id}"
        )
        
        if st.sidebar.button("Refresh", key=f"sidebar_refresh_{service_id}"):
            st.rerun()
        
        with st.spinner("Loading logs..."):
            success, logs = service_manager.get_service_logs(service_id, lines=lines_to_show)
        
        if success:
            if logs.strip():
                # Display logs in sidebar with smaller font
                st.sidebar.text_area(
                    "Logs:",
                    value=logs,
                    height=400,
                    key=f"sidebar_logs_{service_id}"
                )
            else:
                st.sidebar.info("No logs available")
        else:
            st.sidebar.error(f"Failed to retrieve logs: {logs}")
    
    def display_log_search(self, service_id):
        """Display logs with search functionality"""
        st.subheader("Log Search")
        
        # Search controls
        col1, col2 = st.columns([3, 1])
        
        with col1:
            search_term = st.text_input(
                "Search in logs:",
                key=f"search_{service_id}",
                placeholder="Enter search term..."
            )
        
        with col2:
            lines_to_search = st.selectbox(
                "Lines to search:",
                [100, 500, 1000, 2000],
                index=1,
                key=f"search_lines_{service_id}"
            )
        
        if search_term:
            with st.spinner("Searching logs..."):
                success, logs = service_manager.get_service_logs(service_id, lines=lines_to_search)
            
            if success:
                # Filter logs based on search term
                filtered_lines = []
                for line in logs.split('\n'):
                    if search_term.lower() in line.lower():
                        filtered_lines.append(line)
                
                if filtered_lines:
                    st.success(f"Found {len(filtered_lines)} matching lines:")
                    filtered_logs = '\n'.join(filtered_lines)
                    st.code(filtered_logs, language="bash")
                else:
                    st.warning(f"No lines found containing '{search_term}'")
            else:
                st.error(f"Failed to search logs: {logs}")

# Global log viewer instance
log_viewer = LogViewer()
