import os
import re
from datetime import datetime

def validate_hostname(hostname):
    """Validate hostname format"""
    if not hostname:
        return False
    
    # Allow localhost and IP addresses
    if hostname in ['localhost', '127.0.0.1']:
        return True
    
    # Basic hostname validation
    pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    return bool(re.match(pattern, hostname))

def validate_port(port):
    """Validate port number"""
    try:
        port_num = int(port)
        return 1 <= port_num <= 65535
    except (ValueError, TypeError):
        return False

def validate_service_name(service_name):
    """Validate service name format"""
    if not service_name:
        return False
    
    # Service names should be alphanumeric with hyphens and underscores
    pattern = r'^[a-zA-Z0-9_\-]+$'
    return bool(re.match(pattern, service_name))

def validate_log_path(log_path):
    """Validate log file path"""
    if not log_path:
        return True  # Optional field
    
    # Basic path validation
    return log_path.startswith('/') and not '..' in log_path

def format_uptime(seconds):
    """Format uptime in human readable format"""
    if not isinstance(seconds, (int, float)):
        return "Unknown"
    
    days = int(seconds // 86400)
    hours = int((seconds % 86400) // 3600)
    minutes = int((seconds % 3600) // 60)
    
    if days > 0:
        return f"{days}d {hours}h {minutes}m"
    elif hours > 0:
        return f"{hours}h {minutes}m"
    else:
        return f"{minutes}m"

def format_file_size(bytes_size):
    """Format file size in human readable format"""
    if not isinstance(bytes_size, (int, float)):
        return "Unknown"
    
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_size < 1024.0:
            return f"{bytes_size:.1f} {unit}"
        bytes_size /= 1024.0
    return f"{bytes_size:.1f} PB"

def sanitize_input(input_string):
    """Sanitize user input to prevent injection attacks"""
    if not isinstance(input_string, str):
        return ""
    
    # Remove potentially dangerous characters
    dangerous_chars = ['&', '|', ';', '$', '`', '>', '<', '(', ')', '{', '}', '[', ']']
    sanitized = input_string
    
    for char in dangerous_chars:
        sanitized = sanitized.replace(char, '')
    
    return sanitized.strip()

def get_file_age(file_path):
    """Get file age in human readable format"""
    try:
        if os.path.exists(file_path):
            mtime = os.path.getmtime(file_path)
            age = datetime.now().timestamp() - mtime
            return format_uptime(age)
        return "File not found"
    except Exception:
        return "Unknown"

def truncate_text(text, max_length=100):
    """Truncate text to specified length"""
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length-3] + "..."

def is_valid_email(email):
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def generate_safe_filename(filename):
    """Generate safe filename by removing dangerous characters"""
    # Remove or replace dangerous characters
    safe_filename = re.sub(r'[^\w\-_\.]', '_', filename)
    return safe_filename[:255]  # Limit length
