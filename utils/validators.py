import re
from utils.helpers import validate_hostname, validate_port, validate_service_name, validate_log_path, is_valid_email

class ValidationError(Exception):
    """Custom validation error"""
    pass

class InputValidator:
    """Input validation class"""
    
    @staticmethod
    def validate_user_input(username, email, password):
        """Validate user registration input"""
        errors = []
        
        # Username validation
        if not username:
            errors.append("Username is required")
        elif len(username) < 3:
            errors.append("Username must be at least 3 characters long")
        elif len(username) > 50:
            errors.append("Username must be less than 50 characters")
        elif not re.match(r'^[a-zA-Z0-9_]+$', username):
            errors.append("Username can only contain letters, numbers, and underscores")
        
        # Email validation
        if not email:
            errors.append("Email is required")
        elif not is_valid_email(email):
            errors.append("Invalid email format")
        
        # Password validation
        if not password:
            errors.append("Password is required")
        elif len(password) < 6:
            errors.append("Password must be at least 6 characters long")
        elif len(password) > 128:
            errors.append("Password must be less than 128 characters")
        
        return errors
    
    @staticmethod
    def validate_server_input(name, hostname, port, username, key_path):
        """Validate server configuration input"""
        errors = []
        
        # Server name validation
        if not name:
            errors.append("Server name is required")
        elif len(name) > 100:
            errors.append("Server name must be less than 100 characters")
        
        # Hostname validation
        if not hostname:
            errors.append("Hostname is required")
        elif not validate_hostname(hostname):
            errors.append("Invalid hostname format")
        
        # Port validation
        if not validate_port(port):
            errors.append("Port must be between 1 and 65535")
        
        # Username validation
        if not username:
            errors.append("Username is required")
        elif len(username) > 50:
            errors.append("Username must be less than 50 characters")
        
        # Key path validation (optional)
        if key_path and not key_path.startswith('/'):
            errors.append("Key path must be an absolute path")
        
        return errors
    
    @staticmethod
    def validate_service_input(name, service_command, log_path, description):
        """Validate service configuration input"""
        errors = []
        
        # Service name validation
        if not name:
            errors.append("Service name is required")
        elif not validate_service_name(name):
            errors.append("Service name can only contain letters, numbers, hyphens, and underscores")
        elif len(name) > 100:
            errors.append("Service name must be less than 100 characters")
        
        # Service command validation
        if not service_command:
            errors.append("Service command is required")
        elif not validate_service_name(service_command):
            errors.append("Service command can only contain letters, numbers, hyphens, and underscores")
        elif len(service_command) > 255:
            errors.append("Service command must be less than 255 characters")
        
        # Log path validation
        if not validate_log_path(log_path):
            errors.append("Invalid log path format")
        
        # Description validation
        if description and len(description) > 500:
            errors.append("Description must be less than 500 characters")
        
        return errors
    
    @staticmethod
    def validate_login_input(username, password):
        """Validate login input"""
        errors = []
        
        if not username:
            errors.append("Username is required")
        
        if not password:
            errors.append("Password is required")
        
        return errors
    
    @staticmethod
    def validate_permission_input(role_id, service_id, permissions):
        """Validate permission configuration input"""
        errors = []
        
        # Role ID validation
        if not isinstance(role_id, int) or role_id <= 0:
            errors.append("Invalid role ID")
        
        # Service ID validation
        if not isinstance(service_id, int) or service_id <= 0:
            errors.append("Invalid service ID")
        
        # Permissions validation
        valid_permissions = ['can_view', 'can_start', 'can_stop', 'can_restart']
        if not isinstance(permissions, dict):
            errors.append("Permissions must be a dictionary")
        else:
            for perm in permissions:
                if perm not in valid_permissions:
                    errors.append(f"Invalid permission: {perm}")
                if not isinstance(permissions[perm], bool):
                    errors.append(f"Permission {perm} must be a boolean value")
        
        return errors

# Global validator instance
validator = InputValidator()
