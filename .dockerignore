# Git
.git
.gitignore

# Documentation
README.md
DOCKER_DEPLOYMENT.md
*.md

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Database
database/service_portal.db
*.db
*.sqlite

# SSH Keys (will be mounted as volume)
ssh_keys/

# Backups
backups/

# Environment files
.env
.env.local
.env.production

# Docker
docker-compose.yml
docker-compose.override.yml
Dockerfile
.dockerignore

# Scripts (not needed in container)
scripts/

# Temporary files
tmp/
temp/
*.tmp

# Node modules (if any)
node_modules/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/
