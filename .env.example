# Service Management Portal Configuration

# Database Configuration
DATABASE_URL=sqlite:///database/service_portal.db

# Security Settings
SECRET_KEY=change-this-to-a-random-secret-key
SESSION_TIMEOUT=3600

# SSH Configuration
SSH_TIMEOUT=30
SSH_KEY_PATH=~/.ssh/id_rsa

# Application Settings
DEBUG=False
LOG_LEVEL=INFO
MAX_LOG_LINES=1000

# Default Admin User (for initial setup only)
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin123
DEFAULT_ADMIN_EMAIL=<EMAIL>

# Copy this file to .env and modify the values as needed
